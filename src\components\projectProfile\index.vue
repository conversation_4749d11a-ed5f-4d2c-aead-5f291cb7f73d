<template>
  <div class="projectProfile">
    <div class="projectProfile-title" :class="sectionId2 == null ? 'projectProfile-title' : 'projectProfile-title2'"></div>
    <div class="projectProfile-content">
      <div class="text">
        {{ description }}
      </div>
    </div>
  </div>
</template>
    
    
<script setup>
import { onMounted, ref, watch } from "vue";
import api from "../../api/index";
import { useStore } from "vuex";
const store = useStore();

const sectionId2 = ref(store.state.sectionId == '' ? null : store.state.sectionId)

const description = ref("")
onMounted(() => {
  getDescription( {projectId: store.state.projectId, sectionId: store.state.sectionId} )
});

watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    console.log('这个执行了几次')
    sectionId2.value = sectionId
    getDescription({ projectId, sectionId });
  }
);

const getDescription = (params) => {
  api
    .getDescription(params)
    .then((res) => {
      description.value = res.result
      console.log("请求：", res);
    })
    .catch((err) => {
      console.log(err);
    });
};
</script>
    
<style lang="less" scoped>
.projectProfile {
  width: 58.5rem;
  height: 14.5625rem;

  .projectProfile-title {
    background: url("../../assets/img/projectProfile/title.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 28.75rem;
  }

  .projectProfile-title2 {
    background: url("../../assets/img/projectProfile/title2.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 28.75rem;
  }

  .projectProfile-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: calc(100% - 2.25rem);
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    .text {
      font-size: 1rem;
      color: #fff;
      margin: 1.5rem;
      text-align: left;
    }
  }
}
</style>
