<template>
  <div class="projectStatistics">
    <div
      class="projectStatistics-title"
      :class="type != 1 ? 'projectStatistics-title-tracking' : ''"
    ></div>
    <div class="projectStatistics-content">
      <statisticsItem
        class="item item1"
        :isStatistics="type"
        :status="data.designStage"
        title="设计阶段"
        :num="centerData.designComplete"
        :total="centerData.total" 
        :progress="centerData.designComplete / centerData.total * 100"
      ></statisticsItem>
      <statisticsItem
        class="item item2"
        :isStatistics="type"
        :status="data.bidTenderStage"
        title="招投标阶段"
        :num="centerData.bidTenderComplete"
        :total="centerData.total" 
        :progress="centerData.bidTenderComplete / centerData.total * 100"
      ></statisticsItem>
      <statisticsItem
        class="item item3"
        :isStatistics="type"
        :status="data.workPrepare"
        title="开工准备"
        :num="centerData.workPrepareComplete"
        :total="centerData.total" 
        :progress="centerData.workPrepareComplete / centerData.total * 100"
      ></statisticsItem>
      <statisticsItem
        class="item item4"
        :isStatistics="type"
        :status="data.constructionStage"
        title="施工阶段"
        :num="centerData.constructionComplete"
        :total="centerData.total" 
        :progress="centerData.constructionComplete / centerData.total * 100"
      ></statisticsItem>
      <statisticsItem
        class="item item4"
        :isStatistics="type"
        :status="data.completionAcceptanceStage"
        title="竣工验收"
        :num="centerData.completionAcceptanceComplete"
        :total="centerData.total" 
        :progress="centerData.completionAcceptanceComplete / centerData.total * 100"
      ></statisticsItem>
    </div>
  </div>
</template>


<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
const store = useStore();
import statisticsItem from "../projectStatistics/statisticsItem";

const props = defineProps({
  type: Number,
});
let projectId2 = ref(null);
let data = ref({
  bidTenderStage: "",
  completionAcceptanceStage: "",
  constructionStage: "",
  designStage: "",
  workPrepare: "",
});
const statusMapping = {
  finish: "已完成",
  unfinish: "待完成",
  running: "进行中",
};
let centerData = ref({
  bidTenderComplete: 0,
  completionAcceptanceComplete: 0,
  constructionComplete: 0,
  designComplete: 0,
  total: 0,
  workPrepareComplete: 0,
});

onMounted(() => {
  console.log("组件已挂载");

  getProjectStatisticsCenterData({ projectId: store.state.projectId, sectionId: store.state.sectionId });

  getProjectTrackingData({ projectId: store.state.projectId, sectionId: store.state.sectionId });
});

watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    projectId2.value = projectId;
    getProjectTrackingData({ projectId, sectionId });
  }
);

const getProjectStatisticsCenterData = async (params) => {
  let res = await api.getProjectStatisticsCenterData(params);
  console.log("项目统计中心屏：", res);
  centerData.value = res.result;
};

const getProjectTrackingData = async (params) => {
  let res = await api.getProjectTrackingData(params);
  const originalObj = res.result;
  const convertedObj = {};

  for (const key in originalObj) {
    if (originalObj.hasOwnProperty(key)) {
      if (statusMapping.hasOwnProperty(originalObj[key])) {
        convertedObj[key] = statusMapping[originalObj[key]];
      } else {
        convertedObj[key] = originalObj[key];
      }
    }
  }
  data.value = convertedObj;
};
</script>

<style lang="less" scoped>
.projectStatistics {
  width: 28.75rem;
  height: 17.5rem;

  .projectStatistics-title {
    background: url("../../assets/img/projectStatistics/title.png") center
      center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }
  .projectStatistics-title-tracking {
    background: url("../../assets/img/projectStatistics/title-tracking.png")
      center center no-repeat;
    background-size: 100% 100%;
  }

  .projectStatistics-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 14rem;
    position: absolute;
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;
    margin: 0 0.625rem;
    padding-left: 1rem;
    padding-bottom: 1rem;
    overflow: auto;
    .item {
      margin-top: 1rem;
      width: 12.875rem;
      height: 6rem;
      // background-color: yellow;
    }
    .item1 {
    }
    .item2 {
    }
    .item3 {
    }
    .item4 {
    }
  }
}
</style>
