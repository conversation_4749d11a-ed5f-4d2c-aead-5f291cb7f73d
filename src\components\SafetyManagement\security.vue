<template>
  <div class="projectOverview">
    <div
      class="projectOverview-title"
      :class="moduleType == 'QUALITY' ? 'projectOverview-title-QUALITY' : ''"
    ></div>
    <div class="projectOverview-content">
      <!-- <div class="interlayer"></div> -->
      <div class="changx-0" v-if="!store.getters.isSectionScreen">
        <div class="top">
          <div class="left">
            <img
              src="../../assets/img/SafetyManagement/编组 4备份 <EMAIL>"
              alt=""
            />
            <div class="liability_statement">
              <p class="text1">{{ (zrsRate * 100).toFixed(2) }}%</p>
              <p class="text2">责任书签订完成率</p>
            </div>
          </div>
          <div class="right">
            <img
              src="../../assets/img/SafetyManagement/编组 4备份 <EMAIL>"
              alt=""
            />
            <div class="management_system">
              <p class="text1">{{ (glzdRate * 100).toFixed(2) }}%</p>
              <p class="text2">管理制度建设完成率</p>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="form">
            <!-- <el-table :data="tableData" style="width: 100%">
            <el-table-column prop="date" label="Date" width="180" />
            <el-table-column prop="name" label="Name" width="180" />
            <el-table-column prop="address" label="Address" />
          </el-table> -->
            <div class="table__header">
              <div class="text1">{{ store.getters.isProjectScreen == true ? '标段' : '项目' }}名称</div>
              <div class="text2">责任书的签订</div>
              <div class="text3">管理制度建设</div>
            </div>
            <div class="table-tr">
              <div class="item" v-for="(item, index) in tableData" :key="index">
                <div class="te1">{{ item.name }}</div>
                <div
                  class="te2"
                  :class="{
                    'color-a': item.completeZrs == true,
                    'color-b': item.completeZrs == false,
                  }"
                >
                  {{ item.completeZrs ? "已完成" : "未完成" }}
                </div>
                <div
                  class="te3"
                  :class="{
                    'color-a': item.completeGlzd == true,
                    'color-b': item.completeGlzd == false,
                  }"
                >
                  {{ item.completeGlzd ? "已完成" : "未完成" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="changx-1" v-if="store.getters.isSectionScreen">
        <img
        v-if="isFinishZrs == true"
          src="../../assets/img/SafetyManagement/安保体系-已完成.png"
          alt=""
        />
        <img
        v-else
          src="../../assets/img/SafetyManagement/责任书签订-未完成.png"
          alt=""
        />
        <img
        v-if="isFinishGlzd == true"
          src="../../assets/img/SafetyManagement/安保体系-已完成2.png"
          alt=""
        />
        <img
        v-else
          src="../../assets/img/SafetyManagement/安保体系-未完成.png"
          alt=""
        />
      </div>
    </div>
  </div>
</template>
  
  
<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
let store = useStore();
let zrsRate = ref(0);
let glzdRate = ref(0);
let tableData = ref([]);
const isFinishGlzd = ref(false)
const isFinishZrs = ref(false)

const props = defineProps({
  moduleType: String,
});

onMounted(() => {
  campsec();
});
// 监听
let changx = ref(0);

// watch(() => [store.state.projectId, store.state.sectionId],([projectId, sectionId]) => {
    
//   }
// );

// watch(() => [store.state.projectId, store.state.sectionId],([projectId, sectionId]) => {
//   console.log('监听了安保体系接口：',projectId, sectionId)
//     campsec();
//     if (store.state.sectionId) {
//       changx.value = 1;
//     }
// });

watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    campsec();
    if (store.state.sectionId) {
      changx.value = 1;
    }
  });


const manageRegime = async (params) => {
  let res = await api.manageRegime(props.moduleType, params);
  zrsRate.value = res.data.completeRate;
  return res.data.lists;
};

const manageRegime2Section = async (params) => {
  if (params.sectionId == null || params.sectionId == '') {
    return
  }
  let res = await api.manageRegime(props.moduleType, params);
  // zrsRate.value = res.data.completeRate;
  isFinishZrs.value = res.data
  return res.data.lists;
};

const manageRegimeSection = async (params) => {
  let res = await api.manageRegimeSection(props.moduleType, params);
  glzdRate.value = res.completionRate;
  console.log('啧啧啧啧啧啧',res)
  if (params.sectionId != null && params.sectionId != '') {
    isFinishGlzd.value = res
  }
  return res.lists;
};

// 数据合并
const campsec = () => {
  tableData.value = [];
  Promise.all([
    manageRegime({ projectId: store.state.projectId, sectionId: store.state.sectionId }), 
    manageRegimeSection({ projectId: store.state.projectId, sectionId: store.state.sectionId }), 
    manageRegime2Section({ projectId: store.state.projectId, sectionId: store.state.sectionId })])
    .then(([anqx, cost]) => {
      for (let value1 of anqx) {
        for (let value2 of cost) {
          if (value1.name === value2.name) {
            tableData.value.push({ ...value1,completeZrs: value1.complete, ...value2,completeGlzd: value2.complete });
          }
        }
      }
    })
    .catch((err) => {
      // 处理任一请求失败的情况
      console.error("Error fetching data:", err);
    });
};
</script>
  
<style lang="less" scoped>
.projectOverview {
  width: 28.75rem;
  //   height: 38.75rem;

  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/面板标题@2x.png") center
      center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .projectOverview-title-QUALITY {
    background: url("../../assets/img/zhibaotixiBox/面板标题.png") center center
      no-repeat;
    background-size: 100% 100%;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);

    margin-top: 0.3125rem;
    padding-left: 1rem;
    padding-right: 1rem;
    box-sizing: border-box;
    //   position: absolute;
    // .interlayer {
    //   height: 1.25rem;
    // }
    // height: 15.9375rem;
    > .changx-0 {
      width: 100%;
      > .top {
        width: 100%;
        display: flex;
        justify-content: space-evenly;
        > .left {
          display: flex;
          align-items: center;
          > img {
            width: 3rem;
          }
          .liability_statement {
            margin-left: 0.625rem;
            .text1 {
              text-align: left;
              color: #fff;
              height: 1.25rem;
              line-height: 1.25rem;
              font-weight: 700;
              font-size: 1.25rem;
            }
            .text2 {
              font-weight: 400;
              font-size: 0.75rem;
              color: #e2f2fb;
            }
          }
        }
        > .right {
          display: flex;
          align-items: center;

          > img {
            width: 3rem;
          }
          > .management_system {
            margin-left: 0.625rem;
            .text1 {
              text-align: left;
              color: #fff;
              height: 1.25rem;
              line-height: 1.25rem;
              font-weight: 700;
              font-size: 1.25rem;
            }
            .text2 {
              font-weight: 400;
              font-size: 0.75rem;
              color: #e2f2fb;
            }
          }
        }
      }

      > .bottom {
        margin-top: 0.625rem;
        width: 100%;
        height: 10rem;
        .form {
          width: 100%;
          height: 100%;
          // /deep/.el-table{

          //     --el-table-tr-bg-color: rgba(196, 75, 75, 0) !important;
          //     --el-table-bg-color:rgba(223, 0, 0, 0) !important;
          //     --el-table-header-bg-color: rgba(44, 164, 255, 0.322) !important;
          // }
          // /deep/.el-table__header-wrapper{
          //     height: 2rem !important;
          // }

          // /deep/.el-table__header{
          //     width: 100% !important;
          //     height: 2rem;
          // }
          .table__header {
            width: 100%;
            height: 2rem;
            background-color: rgba(44, 164, 255, 0.322);
            display: flex;
            .text1 {
              width: 14.25rem;
              height: 2rem;
              text-align: left;
              color: #2ca3ff;
              padding-left: 0.625rem;
              box-sizing: border-box;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }
            .text2 {
              width: 6.25rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text3 {
              width: 6.25rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }
          }

          .table-tr {
            width: 100%;
            overflow: hidden;
            overflow-y: auto;
            height: 8rem;
            .item {
              width: 100%;
              border-bottom: 0.0625rem solid rgba(44, 164, 255, 0.322);
              display: flex;
              > .te1 {
                width: 14.25rem;
                height: 2rem;
                text-align: left;
                color: #ffffff;
                padding-left: 0.625rem;
                box-sizing: border-box;
                line-height: 2rem;
                font-size: 0.875rem;
                overflow: hidden;
              }
              > .te2 {
                width: 6.25rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }
              > .te3 {
                width: 6.25rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }
            }
          }
        }
      }
    }

    > .changx-1 {
      width: 100%;
      height: 9rem;
      display: flex;
      justify-content: space-between;
      align-items: center;
      > img {
        width: 12.625rem;
        height: 5.5rem;
      }
    }
  }
}

.color-a {
  color: #3dffff;
}
.color-b {
  color: #ff5046;
}
</style>