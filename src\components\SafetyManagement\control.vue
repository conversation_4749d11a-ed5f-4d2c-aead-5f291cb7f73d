<template>
  <div class="projectOverview">
    <div class="projectOverview-title"></div>
    <div class="projectOverview-content">
      <div class="left-box">
        <div class="weiximg">
          <div class="imgx"></div>
          <div id="shanx" style="width: 100%; height: 100%"></div>
        </div>
        <img
          class="img1"
          src="../../assets/img/SafetyManagement/隔开线.png"
          alt=""
        />
      </div>
      <div class="right-box">
        <div id="zhuz" style="width: 100%; height: 100%"></div>
      </div>
    </div>
  </div>
</template>
  
  
  <script setup>
import { onMounted, ref, watch, onUpdated,onBeforeUnmount } from "vue";
import overviewItem from "../projectOverview/overviewItem.vue";
import api from "@/api/index";
import * as echarts from "echarts";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
const route = useRoute();
let store = useStore();
onMounted(() => {
  // if (!store.state.projectId && !store.state.sectionId) {
    riska();
    presencex()
  // }
});
// 监听
watch(
  () => [store.state.projectId, store.state.sectionId],
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      riska();
      presencex()
    }
  }
);
// 风险接口
const riska = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  api
    .risk(params)
    .then((res) => {
      let data = res.data;
      if (route.path == "/safetyManagement") {
        shanxing1(data);
      }
    })
    .catch((err) => {
      console.log(err, "风险接口请求失败");
    });
};

// 在场人员统计
const presencex = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  api
    .presence(params)
    .then((res) => {
      let data = res.data;
      if (route.path == "/safetyManagement") {
        zhuzt(data)
      }
    })
    .catch((err) => {
      console.log(err, "在场人员统计接口请求失败");
    });
};
const dataroute = ref(null);
// 柱状统计图
const zhuzt = async (data) => {
  let zhuzElement = document.getElementById("zhuz");
  // 销毁已有的 echarts 实例
  echarts.dispose(zhuzElement);

  let zhuz = await echarts.init(document.getElementById("zhuz"));
  let zhuzoptionx = ref({
    xAxis: {
      name: "人数",
      // max: 600,
      type: "value",
      nameTextStyle: {
        fontSize: 12,
        color: "rgba(255, 255, 255, 0.6)",
      },
      axisLabel: {
        textStyle: {
          fontSize: 12, // 设置字体大小
          color: "#D3DEEC", // 设置字体颜色
          fontWeight: 400,
        },
      },
    },
    yAxis: {
      name: "状态",
      type: "category",
      nameLocation: "start",
      nameTextStyle: {
        fontSize: 12,
        color: "rgba(255, 255, 255, 0.6)",
      },
      data: [
        {
          value: "在场人数",
          textStyle: { fontSize: 12, color: "#D3DEEC", fontWeight: 400 },
        },
        {
          value: "累计在场人数",
          textStyle: { fontSize: 12, color: "#D3DEEC", fontWeight: 400 },
        },
      ],
      inverse: true,
    },
    grid: {
      top: "22%",
      left: "3%",
      right: "11%",
      bottom: "5%",
      containLabel: true,
    },
    series: [
      {
        data: [
          {
            value: data.presenceNumber,
            // type: "bar",
            name: "在场人数",
            label: {
              show: true,
              position: "right", // 可根据需要调整标签位置
              textStyle: {
                fontSize: 12, // 设置标签字体大小
                color: "#fff", // 设置标签字体颜色
                fontWeight: 500,
              },
            },
            itemStyle: {
              showBackground: true,
              barBorderRadius: [0, 8, 8, 0], // 设置圆角，数组内四个值分别为左上、右上、右下、左下的圆角半径
              color: {
                type: "linear",
                x: 1, // 横向渐变从左到右
                y: 0, // 垂直方向上渐变没有变化，保持0
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: "#FFFFFF", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: "#3DFFFF", // 50% 处的颜色
                  },
                  {
                    offset: 0.9,
                    color: "#023164", // 90% 处的颜色
                  },
                ],
              },
            },
          },
          {
            value: data.presenceTotal,
            // type: "bar",
            name: "累计在场人数",
            label: {
              show: true,
              position: "right", // 可根据需要调整标签位置
              textStyle: {
                fontSize: 12, // 设置标签字体大小
                color: "#fff", // 设置标签字体颜色
                fontWeight: 500,
              },
            },
            itemStyle: {
              showBackground: true,
              barBorderRadius: [0, 8, 8, 0], // 设置圆角，数组内四个值分别为左上、右上、右下、左下的圆角半径
              color: {
                type: "linear",
                x: 1, // 横向渐变从左到右
                y: 0, // 垂直方向上渐变没有变化，保持0
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: "#FFFFFF", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: "#4798F7", // 自定义第二个柱状图的颜色
                  },
                  {
                    offset: 0.9,
                    color: "#023164", // 自定义第二个柱状图的颜色
                  },
                ],
              },
            },
          },
        ],
        type: "bar",
        barWidth: 12,
        showBackground: true,
        backgroundStyle: {
          color: "rgba(180, 180, 180, 0.2)",
          borderRadius: [0, 12, 12, 0], // 柱状图的圆角设置
        },
      },
    ],
  });
  zhuz.setOption(zhuzoptionx.value);
};
function formattedValue(value) {
    return Number(value).toFixed(2);
};

// 扇形统计图
const shanxing1 = async (data) => {
  console.log(data, "cessadads");
  let shanxElement = document.getElementById("shanx");
  // 销毁已有的 echarts 实例
  if (shanxElement) {
    echarts.dispose(shanxElement);
  }

  const shanx = await echarts.init(document.getElementById("shanx"));
  const datas = ref([
    [
      {
        name: "较大风险",
        value: 1.5,
        baifbi: `${data?.majorRisks ? data?.majorRisks : 0}/${
          data?.majorRiskRatio ? formattedValue(data?.majorRiskRatio * 100) : 0
        }%`,
        itemStyle: { color: "#EE7F46" },
      },
      {
        name: "一般风险",
        value: 3,
        baifbi: `${data?.generalRisks ? data?.generalRisks : 0}/${
          data?.generalRiskRatio ? formattedValue(data?.generalRiskRatio * 100) : 0
        }%`,
        itemStyle: { color: "#F7C034" },
      },
      {
        name: "重大风险",
        value: 0.5,
        baifbi: `${data?.significantRisks ? data?.significantRisks : 0}/${
          data?.significantRiskRatio ? formattedValue(data?.significantRiskRatio * 100) : 0
        }%`,
        itemStyle: { color: "#FF5046" },
      },
      {
        name: "低风险",
        value: 6,
        baifbi: `${data?.lowRisks ? data?.lowRisks : 0}/${
          data?.lowRiskRatio ? formattedValue(data?.lowRiskRatio * 100) : 0
        }%`,
        itemStyle: { color: "#4798F7" },
      },
    ],
  ]);

  const option = ref({
    // title: {
    //   text: "阅读书籍分布",
    //   left: "center",
    //   textStyle: {
    //     color: "#999",
    //     fontWeight: "normal",
    //     fontSize: 14,
    //   },
    // },
    series: datas.value.map(function (data, idx) {
      var top = idx * 33.3;
      return {
        type: "pie",
        // radius: [20, 60],
        top: top + "%",

        left: "center",
        width: 300,
        center: ["50%", "50%"],
        radius: ["60%", "70%"],
        itemStyle: {
          // borderColor: "#1F1F1F",
          borderWidth: 2,
        },
        label: {
          alignTo: "edge",
          // formatter: "{name|{b}}\n{time|{c} 小时}",
          formatter: function (params) {
            // params 为 formatter 的回调函数参数，包含了当前数据项的信息
            return `{name|${params.name}}\n{baifbi|${params.data.baifbi}}`;
          },

          minMargin: 5,
          edgeDistance: 10,
          lineHeight: 15,
          rich: {
            time: {
              fontSize: 10,
              color: "#fff",
            },
            name: {
              fontSize: 12,
              lineHeight: 18,
              color: "#D3DEEC", // 设置name的颜色
            },
            baifbi: {
              fontSize: 12,
              lineHeight: 18,
              color: "#fff", // 设置baifbi的颜色
            },
          },
        },
        labelLine: {
          length: 1,
          length2: 0,
          maxSurfaceAngle: 8,
        },

        data: data,
      };
    }),
  });

  shanx.setOption(option.value);
};
</script>
  
  <style lang="less" scoped>
// #zhuz > * {
//   /* 具体的图形元素 */
//   position: absolute;
//   top: 0;
//   left: 0;
//   transform: scale(1.1); /* 缩放因子，这里放大120% */
// }
.projectOverview {
  width: 58.5rem;
  height: 13.5625rem;
  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/安全管控.png") center
      center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 28.75rem;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 11.0625rem;
    width: 58.5rem;
    padding: 1rem;
    box-sizing: border-box;
    display: flex;
    margin-top: .3125rem;
    > .left-box {
      width: 50%;
      height: 100%;
      display: flex;
      > .weiximg {
        width: 100%;
        position: relative;
        > .imgx {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 7.5rem;
          height: 7.5rem;
          background: url("../../assets/img/SafetyManagement/安全管控统计图.png")
            center center no-repeat;
          background-size: 100% 100%;
        }
      }
      > .img1 {
      }
    }
    > .right-box {
      width: 50%;
      height: 100%;
    }
  }
}
</style>