// src/components/layout/util/index.js
 
import store from "@/store/index";
 
export const LAYOUT_TYPE = {
  HEAD: "top",
  FOOT: "bottom"
};
 
// 重新计算布局，当head和foot中的元素在业务逻辑中动态的显示或者隐藏会改变容器的高度，因此需要调用这个方法重新计算
export const resetLayout = function(type) {
  this.$nextTick(() => {
    if (!type || type == LAYOUT_TYPE.HEAD) {
      let head = document.getElementById("mHead");
      setState(type, head.getBoundingClientRect().height + "px");
    }
    if (!type || type == LAYOUT_TYPE.FOOT) {
      let foot = document.getElementById("mFoot");
      setState(type, foot.getBoundingClientRect().height + "px");
    }
  });
};
 
// 保存到vuex
export const setState = (type, height) => {
  let commitName = type === LAYOUT_TYPE.HEAD ? "setTop" : "setBottom";
  store.commit(commitName, height);
};
 
// 设备类型是否是IOS手机
export const isIOS = !!navigator.userAgent.match(
  /\(i[^;]+;( U;)? CPU.+Mac OS X/
);