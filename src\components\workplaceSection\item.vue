<template>
  <div class="content">
    <div class="content-item-bg">
      <div class="top-line"></div>
      <div class="bottom-line"></div>
      <div class="content-content">
        <div class="content-left">
          <div class="left-icon" :style="{'background':`url(${url}) no-repeat center`}"></div>
          <div class="left-text">{{ title }}</div>
        </div>
        <div class="content-right">{{ content }}</div>
      </div>
    </div>
  </div>
</template>
      
      <script setup>
import { onMounted, ref } from "vue";

const props = defineProps({
  title: String,
  content: String,
  type: Number,
});

let url = ref(require("../../assets/img/workplace/icon001.png"))

if (props.type == 1) {
  url = ref(require("../../assets/img/workplace/icon001.png"))
}else if (props.type == 2) {
  url = ref(require("../../assets/img/workplace/icon002.png"))
}else if (props.type == 3) {
  url = ref(require("../../assets/img/workplace/icon003.png"))
}else if (props.type == 4) {
  url = ref(require("../../assets/img/workplace/icon004.png"))
}else if (props.type == 5) {
  url = ref(require("../../assets/img/workplace/icon005.png"))
}else if (props.type == 6) {
  url = ref(require("../../assets/img/workplace/icon006.png"))
}

onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>
      
<style lang="less" scoped>
.content {
  .content-item-bg {
    border: 0.0625rem rgba(79, 160, 255, 0.2) solid;
    width: 100%;
    height: 100%;
    position: relative;

    .top-line {
      background: url("../../assets/img/workplace/top-line.png") center center
        no-repeat;
      background-size: 100% 100%;
      height: 0.0625rem;
      width: 4rem;
      position: absolute;
    }

    .bottom-line {
      background: url("../../assets/img/workplace/bottom-line.png") center
        center no-repeat;
      background-size: 100% 100%;
      height: 0.0625rem;
      width: 4rem;
      position: absolute;
      right: 0;
      bottom: 0;
    }
    .content-content {
      height: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 1rem;
      .content-left {
        display: flex;
      align-items: center;
        .left-icon {
          // background: url("../../assets/img/workplace/icon001.png") center
          //   center no-repeat;
          background-size: 100% 100% !important;
          height: 1rem;
          width: 1rem;
          margin-right: 0.5rem;
        }
      }
    }
  }
}
</style>