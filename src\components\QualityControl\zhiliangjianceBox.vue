<template>
  <div class="zhiliangjianceBox">
    <div class="top-box">

    </div>
    <div class="title-box">
      <img src="../../assets/img/zhiliangjianceBox/面板标题.png" alt="" />
    </div>
    <div class="neirong-box">
      <div class="neirong-item">
        <div class="box-top">
          <img src="../../assets/img/zhiliangjianceBox/施工06 1.png" alt="" />
          <p>施工单位</p>
        </div>
        <div class="xinxi-box">
          <div class="xinxi-item">
            <div class="xinxi-title">
              <div class="kuai"></div>
              <p>原材料检测</p>
            </div>
            <p class="p-text">计划检测： {{ materialData.construction.rawMaterialPlan }}次</p>
            <p class="p-text">实际检测： {{ materialData.construction.rawMaterialActual }}次</p>
            <div class="hegelv">
              <p>完成率 :</p>
              <div class="jindutiao">
                <div class="jindu" :style="`width: ${ materialData.construction.rawMaterialRatio * 5.6875 }rem`"></div>
              </div>
              <p>{{ (materialData.construction.rawMaterialRatio * 100).toFixed(2) }}%</p>
            </div>
          </div>
          <div class="xinxi-item xinxi-item2">
            <div class="xinxi-title">
              <div class="kuai"></div>
              <p>中间产品检测</p>
            </div>
            <!-- <p class="p-text">计划检测： 20次</p> -->
            <p class="p-text">   </p>
            <p class="p-text">实际检测： {{ materialData.construction.middleProductActual }}次</p>
            <!-- <div class="hegelv">
              <p>完成率 :</p>
              <div class="jindutiao">
                <div class="jindu"></div>
              </div>
              <p>32%</p>
            </div> -->
          </div>
        </div>
      </div>
      <div class="neirong-item">
        <div class="box-top">
          <img src="../../assets/img/zhiliangjianceBox/监理单位 (1) 1.png" alt="" />
          <p>监理单位</p>
        </div>
        <div class="xinxi-box">
          <div class="xinxi-item">
            <div class="xinxi-title">
              <div class="kuai"></div>
              <p>原材料检测</p>
            </div>
            <p class="p-text">计划检测： {{ materialData.supervision.rawMaterialPlan }}次</p>
            <p class="p-text">实际检测： {{ materialData.supervision.rawMaterialActual }}次</p>
            <div class="hegelv">
              <p>完成率 :</p>
              <div class="jindutiao">
                <div class="jindu" :style="`width: ${ materialData.supervision.rawMaterialRatio * 5.6875 }rem`"></div>
              </div>
              <p>{{ (materialData.supervision.rawMaterialRatio * 100).toFixed(2) }}%</p>
            </div>
          </div>
          <div class="xinxi-item xinxi-item2">
            <div class="xinxi-title">
              <div class="kuai"></div>
              <p>中间产品检测</p>
            </div>
            <p class="p-text">   </p>
            <p class="p-text">实际检测： {{ materialData.supervision.middleProductActual }}次</p>
            <!-- <div class="hegelv">
              <p>完成率 :</p>
              <div class="jindutiao">
                <div class="jindu"></div>
              </div>
              <p>32%</p>
            </div> -->
          </div>
        </div>
      </div>
      <div class="neirong-item">
        <div class="box-top">
          <img src="../../assets/img/zhiliangjianceBox/分包单位 (1) 1.png" alt="" />
          <p>第三方抽检</p>
        </div>
        <div class="xinxi-box">
          <div class="xinxi-item">
            <div class="xinxi-title">
              <div class="kuai"></div>
              <p>原材料检测</p>
            </div>
            <p class="p-text">计划检测： {{ materialData.thirdParty.rawMaterialPlan }}次</p>
            <p class="p-text">实际检测： {{ materialData.thirdParty.rawMaterialActual }}次</p>
            <div class="hegelv">
              <p>完成率 :</p>
              <div class="jindutiao">
                <div class="jindu" :style="`width: ${ materialData.thirdParty.rawMaterialRatio * 5.6875 }rem`"></div>
              </div>
              <p>{{ (materialData.thirdParty.rawMaterialRatio * 100).toFixed(2) }}%</p>
            </div>
          </div>
          <div class="xinxi-item xinxi-item2">
            <div class="xinxi-title">
              <div class="kuai"></div>
              <p>中间产品检测</p>
            </div>
            <p class="p-text">    </p>
            <p class="p-text">实际检测： {{ materialData.thirdParty.middleProductActual }}次</p>
            <!-- <div class="hegelv">
              <p>完成率 :</p>
              <div class="jindutiao">
                <div class="jindu"></div>
              </div>
              <p>32%</p>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, onBeforeMount, onMounted, watch } from "vue";
import api from "@/api/index.js";
import { useStore } from "vuex";
export default {
  name: "",
  setup() {
    console.log("1-开始创建组件-setup");
    const store = useStore();
    const data = reactive({
      materialData: {
        construction: {
          middleProductActual: 0,
          rawMaterialActual: 0,
          rawMaterialPlan: 0,
          rawMaterialRatio: 0
        },
        supervision: {
          middleProductActual: 0,
          rawMaterialActual: 0,
          rawMaterialPlan: 0,
          rawMaterialRatio: 0
        },
        thirdParty:{
          middleProductActual: 0,
          rawMaterialActual: 0,
          rawMaterialPlan: 0,
          rawMaterialRatio: 0
        }
      },
    });
    onBeforeMount(() => {
      console.log("2.组件挂载页面之前执行----onBeforeMount");
    });
    onMounted(() => {
      console.log("3.-组件挂载到页面之后执行-------onMounted");
      material({
        projectId: store.state.projectId,
        sectionId: store.state.sectionId,
      });
    });
    watch(
      () => [store.state.projectId, store.state.sectionId],
      ([projectId, sectionId]) => {
        material({ projectId, sectionId });
      }
    );
    const material = async (params) => {
      let res = await api.material(params);
      console.log("质量检测-：", res);
      data.materialData = res;
    };
    return {
      ...toRefs(data),
    };
  },
};
</script>
<style scoped lang='less'>
.zhiliangjianceBox {
  margin-top: 1rem;
  width: 100%;
  height: 100%;

  .title-box {
    width: 100%;
    height: 2rem;

    img {
      width: 100%;
    }
  }

  .neirong-box {
    margin-top: .3125rem;
    width: 100%;
    height: 35.3125rem;
    background: #2e60b91a;
    padding: .875rem 1rem;
    box-sizing: border-box;
    font-size: .875rem;
    overflow: hidden;
    overflow-y: auto;

    .neirong-item {
      width: 100%;
      height: 10.75rem;
      border: .0625rem solid #2e60b9;
      margin: 1rem 0;

      .box-top {
        height: 2.5rem;
        width: 100%;
        display: flex;
        background: linear-gradient(90deg,
            rgba(71, 152, 247, 0.2) 0%,
            rgba(71, 152, 247, 0) 100%);

        img {
          width: 1.25rem;
          height: 1.25rem;
          margin: .625rem;
        }

        p {
          line-height: 2.5rem;
          font-size: 1.125rem;
          font-weight: 700;
          color: #fff;
        }
      }

      .xinxi-box {
        width: 100%;
        height: 8.25rem;
        padding: .875rem 1rem;
        box-sizing: border-box;
        text-align: left;
        display: flex;

        .xinxi-item {
          width: 13.3rem;
          height: 6.25rem;
          position: 0 1rem 0 0;
          border-right: .0625rem dashed #fff;

          &:last-child {
            border: 0;
            margin: 0 0 0 1rem;
          }

          .xinxi-title {
            height: 1.25rem;
            width: 100%;
            display: flex;
            margin: 0 0 .5rem 0;

            .kuai {
              width: .25rem;
              height: .75rem;
              background-color: #2ca3ff;
              margin: .25rem .25rem .25rem 0;
            }

            p {
              font-size: .875rem;
              line-height: 1.25rem;
            }
          }

          .p-text {
            height: 1.125rem;
            line-height: 1.125rem;
            margin: .5rem 0;
          }

          .hegelv {
            width: 100%;
            height: 1.125rem;
            display: flex;

            p {
              line-height: 1.125rem;
            }

            .jindutiao {
              width: 5.6875rem;
              height: .375rem;
              border-radius: .1875rem;
              background: #3d8aff33;
              margin: .5rem .25rem;
              display: flex;

              .jindu {
                // width: 3.0625rem;
                height: 100%;
                border-radius: .1875rem;
                background: linear-gradient(90deg,
                    #023164 0%,
                    #4798f7 71.71%,
                    #ffffff 99.42%);
              }
            }
          }
        }
        .xinxi-item2 {
          width: 9.375rem;
        }
      }
    }
  }
}
</style>