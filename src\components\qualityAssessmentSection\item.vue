<template>
  <div class="content">
    <div class="content-item-bg">
      <div class="content-content">
        <div class="content-top">
          <div class="top-line"></div>
          <div class="top-text">{{ item.title }}</div>
        </div>
        <div class="content-bottom">
          <div class="pie">
            <div class="shanx"></div>
            <div :id="'santj2' + item.id" style="width: 9.25rem; height: 9.25rem"></div>
            <div class="textimg1-2">
              <p style="font-weight: 700; font-size: 1.5rem">{{ item.value.total }}</p>
              <p style="font-weight: 400; font-size: 0.75rem">{{ item.title }}</p>
            </div>
          </div>
          <div class="des">
            <div class="des-item">
              <div class="des-dot"></div>
              <div class="des-text">未评定</div>
              <div class="des-num">{{ item.value.toEvaluation }}</div>
              <div class="des-percent">{{ item.value.toEvaluation == 0 ? 0 : (item.value.toEvaluation / item.value.total * 100).toFixed(2)}}%</div>
            </div>
            <div class="des-item">
              <div class="des-dot des-dot2"></div>
              <div class="des-text">评定中</div>
              <div class="des-num">{{ item.value.inEvaluation }}</div>
              <div class="des-percent">{{ item.value.inEvaluation == 0 ? 0 : (item.value.inEvaluation / item.value.total * 100).toFixed(2)}}%</div>
            </div>
            <div class="des-item">
              <div class="des-dot des-dot3"></div>
              <div class="des-text">已评定</div>
              <div class="des-num">{{ item.value.evaluated }}</div>
              <div class="des-percent">{{ item.value.evaluated == 0 ? 0 : (item.value.evaluated / item.value.total * 100).toFixed(2)}}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
        
<script setup>
import { onMounted, ref } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  item: Object
});

onMounted(() => {
  console.log("组件已挂饼图");
  loadEcharts();
});

const loadEcharts = async (data) => {
  let santj2Element = document.getElementById("santj2" + props.item.id);
  // 销毁已有的 echarts 实例
  echarts.dispose(santj2Element);

  const santj2 = await echarts.init(document.getElementById("santj2" + props.item.id));
  const canto2 = ref({
    tooltip: {
      trigger: "item",
    },
    legend: {
      top: "5%",
      left: "center",
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        center: ["50%", "50%"],
        radius: ["60%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          // normal: {
          //   color: "blue",
          //   opacity: 1,
          //   transform: `scale: (1.2)`,
          // },

          // borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            value: props.item.value.toEvaluation ? props.item.value.toEvaluation : 0,
            itemStyle: { color: "#2CA3FF" },
          },
          {
            value: props.item.value.inEvaluation
              ? props.item.value.inEvaluation
              : 0,
            itemStyle: { color: "#F7C034" },
          },
          {
            value: props.item.value.evaluated
              ? props.item.value.evaluated
              : 0,
            itemStyle: { color: "#3DFFFF" },
          }
        ],
      },
    ],
  });

  santj2.setOption(canto2.value);
};

const loadEcharts2 = () => {
  var myChart = echarts.init(document.querySelector(".bottom-pieChart"));
  myChart.setOption({
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "vertical",
      left: "right",
      top: "center",
    },
    series: [
      {
        name: "Access From",
        type: "pie",
        radius: ["40%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 1048, name: "Search Engine" },
          { value: 735, name: "Direct" },
          { value: 580, name: "Email" },
          { value: 484, name: "Union Ads" },
          { value: 300, name: "Video Ads" },
        ],
      },
    ],
  });
};
</script>
        
<style lang="less" scoped>
.content {
  height: 14.3rem;
  .content-item-bg {
    border: 0.0625rem rgba(79, 160, 255, 0.2) solid;
    width: 100%;
    height: 100%;
    position: relative;

    .content-content {
      height: 100%;
      .content-top {
        display: flex;
        align-items: center;
        margin-left: 1rem;
        margin-top: 1rem;
        .top-line {
          width: 0.25rem;
          height: 0.75rem;
          background-color: #2ca3ff;
        }
        .top-text {
          margin-left: 0.5rem;
        }
      }
      .content-bottom {
        height: calc(100% - 1.375rem);
        display: flex;
        align-items: center;
        margin-top: -1rem;
        .pie {
          width: 9.25rem;
          height: 9.25rem;
          margin: 0 3.75rem;
          position: relative;
          background: url("../../assets/img/SafetyManagement/扇形统计3.png")
            center center no-repeat;
          background-size: 100% 100%;
          .shanx {
            width: 8rem;
            height: 8rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: url("../../assets/img/SafetyManagement/扇形浅蓝2.png")
              center center no-repeat;
            background-size: 100% 100%;
          }
          .textimg1-2 {
            width: 5.25rem;
            height: 5.25rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: url("../../assets/img/SafetyManagement/扇形浅蓝.png")
              center center no-repeat;
            background-size: 100% 100%;
          }
        }
        .des{
            display: flex;
            flex-direction: column;
            
            .des-item{
                display: flex;
                align-items: center;
                margin-left: -1rem;
                margin-top: 0.5rem;
             .des-dot{
                width: 0.5rem;
                height: 0.5rem;
                background-color: #2CA3FF;
             }   
             .des-dot2{
              background-color: #F7C034;
             }
             .des-dot3{
              background-color: #3DFFFF;
             }
             .des-text{
                margin-left: 0.5rem;
             }

            }
            .des-num{
                // margin-left: 4rem;
                position: absolute;
                right: 6.2rem;
            }
            .des-percent{
                margin-left: 1rem;
                position: absolute;
                right: 1rem;
            }
        }
      }
    }
  }
}
</style>