import { createStore } from 'vuex'

export default createStore({
  state: {
    projectId:localStorage.getItem('projectId') == 'null' ? '' : localStorage.getItem('projectId'),// 项目ID
    sectionId:localStorage.getItem('sectionId') == 'null' ? '' : localStorage.getItem('sectionId'),// 标段ID
    zss: ''
  },
  getters: {
    getProjectId(state) {
      return state.projectId
    },
    isProjectScreen(state) {
      console.log('是不是项目屏：',state.projectId, state.projectId != '' , state.projectId != undefined, state.projectId != 'null')
      return state.projectId && state.projectId != '' && state.projectId != 'null'
    },
    isSectionScreen(state) {
      console.log('是不是标段屏：',state.sectionId, state.sectionId != '' , state.sectionId != undefined, state.sectionId != 'null')
      let isSection = state.sectionId && state.sectionId != '' && state.sectionId != 'null'
      console.log('是不是标段屏啊：',isSection)
      isSection = (isSection == '' || isSection == null) ? false : true
      return isSection
    }
  },
  mutations: {
    updatePorjectId(state, projectId) {
      localStorage.setItem('projectId', projectId == 'null' ? '' : projectId)
      state.projectId = projectId
    },
    updateSectionId(state, sectionId) {
      localStorage.setItem('sectionId', sectionId == 'null' ? '' : sectionId)
      state.sectionId = sectionId
    },
    updateZss(state, zss) {
      state.zss = zss
    }
  },
  actions: {
  },
  modules: {
  }
})
