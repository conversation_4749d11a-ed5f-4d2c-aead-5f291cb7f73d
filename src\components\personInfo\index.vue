<template>
  <div class="personInfo">
    <div class="personInfo-title"></div>
    <div class="personInfo-content">
      <div class="content-content">
        <!-- <item class="item" v-for="item in data" :key="item" :item="item"></item> -->
        <item class="item" v-for="cell in data" :key="cell.role" :item="cell"></item>
      </div>
    </div>
  </div>
</template>
  
<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
import item from "./item.vue";
import itemView from "./item.vue";
const store = useStore();
let projectId2 = ref(null);
let data = ref([]);

onMounted(() => {
  getPersonInfoData({ projectId: store.state.projectId, sectionId: store.state.sectionId });
});

watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    projectId2.value = projectId;
    getPersonInfoData({ projectId, sectionId });
  }
);

const roleMap = new Map([
  ['PM', '项目经理'],
  ['QI', '质检员'],
  ['SO', '安全员'],
  ['CO', '施工员'],
  ['DO', '技术负责']
]);

const getPersonInfoData = async (params) => {
  let res = await api.getPersonInfoData(params);
  data.value = res.result
  data.value.forEach(it => it.role = roleMap.get(it.role))
};
</script>
  
  <style lang="less" scoped>
.personInfo {
  width: 28.75rem;
  height: 17.5rem;

  .personInfo-title {
    background: url("../../assets/img/personInfo/title.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .personInfo-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: calc(100% - 2.25rem - 1.25rem);
    width: 100%;
    position: absolute;
    overflow: auto;
    padding: 0.625rem 1.25rem;
    .content-content {
      margin: 0.625rem 0;
      .item {
        width: calc(100% - 2rem);
        height: 1.25rem;
        margin-bottom: 1.5rem;
      }
    }
  }
}
</style>
  
