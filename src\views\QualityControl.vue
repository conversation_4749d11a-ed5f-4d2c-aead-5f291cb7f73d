<template>
  <div class="QualityControl">
    <div class="left-box">
      <div class="zhibaotixi">
        <!-- <zhibaotixiBox></zhibaotixiBox> -->
        <security moduleType="QUALITY"></security>
      </div>
      <div class="zhijianpingding" v-if="sectionId2 == null">
        <zhijianpingdingBox></zhijianpingdingBox>
      </div>
      <qualityAssessmentSection
        v-else
        class="qualityAssessmentSection"
      ></qualityAssessmentSection>
    </div>
    <div class="zhong-box">
      <div class="anquanjiancha">
        <anquanjianchaBox></anquanjianchaBox>
      </div>
      <div class="jianchayingxiang">
        <jianchayingxiangBox></jianchayingxiangBox>
      </div>
  
    </div>
    <div class="right-box">
      <div class="jishujiaodi">
        <technicalDisclosure></technicalDisclosure>
      </div>
      <div class="zhiliangjiance">
        <zhiliangjianceBox></zhiliangjianceBox>
      </div>
    </div>
  </div>
</template>

<script>
import zhibaotixiBox from "../components/QualityControl/zhibaotixiBox.vue";
import security from "../components/SafetyManagement/security.vue";
import zhijianpingdingBox from "../components/QualityControl/zhijianpingdingBox.vue";
import jianchayingxiangBox from "../components/QualityControl/jianchayingxiangBox.vue";
import anquanjianchaBox from "../components/QualityControl/anquanjianchaBox.vue";
import zhiliangjianceBox from "../components/QualityControl/zhiliangjianceBox.vue";
import technicalDisclosure from "../components/technicalDisclosure/index.vue";
import qualityAssessmentSection from "../components/qualityAssessmentSection/index.vue";
import { reactive, toRefs, onBeforeMount, onMounted, watch } from "vue";
import { useStore } from "vuex";
export default {
  name: "",
  components: {
    zhibaotixiBox,
    security,
    zhijianpingdingBox,
    jianchayingxiangBox,
    anquanjianchaBox,
    zhiliangjianceBox,
    technicalDisclosure,
    qualityAssessmentSection,
  },
  setup() {
    // let sectionId2 = ref(null)
    console.log("1-开始创建组件-setup");
    const store = useStore();
    const data = reactive({ sectionId2: store.state.sectionId == '' ? null : store.state.sectionId });

    onBeforeMount(() => {
      console.log("2.组件挂载页面之前执行----onBeforeMount");
    });
    onMounted(() => {
      console.log("3.-组件挂载到页面之后执行-------onMounted");
    });
    watch(
      () => [store.state.sectionId],
      ([sectionId]) => {
        data.sectionId2 = sectionId;
      }
    );

    return {
      ...toRefs(data),
    };
  },
};
</script>
<style scoped lang='less'>
.QualityControl {
  width: 100%;
  // height: calc(100% - 9.0625rem);
  display: flex;
  justify-content: space-around;
  margin-top: .5rem;
  .left-box {
    width: 28.75rem;
    // height: 100%;
    margin: 0 1rem;
    .zhibaotixi {
      width: 100%;
      // height: 18.4375rem;
    }
    .zhijianpingding {
      width: 100%;
      height: 37.8125rem;
    }
    .qualityAssessmentSection {
      width: 100%;
      height: 37.8125rem;
    }
  }
  .zhong-box {
    width: 58.5rem;
    .zhijianpingding {
      width: 100%;
      height: 44.6875rem;
    }
    .anquanjiancha {
      width: 100%;
      height: 11.5625rem;
    }
  }
  .right-box {
    width: 28.75rem;
    margin: 0 1rem;
    .jishujiaodi {
      width: 100%;
      height: 18.4375rem;
    }
    .zhiliangjiance {
      width: 100%;
      height: 37.8125rem;
    }
  }
}
</style>