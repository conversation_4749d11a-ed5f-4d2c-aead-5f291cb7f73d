<template>
  <div id="appx">
    <div class="left">
      <security moduleType="SECURITY"></security>
      <engineering_proposal style="margin-top: 1rem;"></engineering_proposal>
      <safety_production style="margin-top: 1rem;"></safety_production>
    </div>
    <div class="centre">
      <security_check></security_check>
      <hidden_danger style="margin-top: 1rem;"></hidden_danger>
      <education style="margin-top: 1rem;"></education>
      <control style="margin-top: 1rem;"></control>
    </div>
    <div class="right">
      <emergency></emergency>
      <special_operation style="margin-top: 1rem;"></special_operation>
      <safety_campaign style="margin-top: 1rem;"></safety_campaign>
    </div>
  </div>
</template>

<script >
import { ref, reactive, toRefs, onBeforeMount, onMounted,watch } from "vue";
import { useStore } from "vuex";
import security from "../components/SafetyManagement/security.vue";
import engineering_proposal from "../components/SafetyManagement/engineering_proposal.vue";
import safety_production from "../components/SafetyManagement/safety_production.vue";
import security_check from "../components/SafetyManagement/security_check.vue";
import hidden_danger from "../components/SafetyManagement/hidden_danger";
import education from "../components/SafetyManagement/education.vue";
import control from "../components/SafetyManagement/control.vue";
import emergency from "../components/SafetyManagement/emergency.vue";
import special_operation from "../components/SafetyManagement/special_operation.vue";
import safety_campaign from "../components/SafetyManagement/safety_campaign.vue";
export default {
  name: "",
  components: {
    security,
    engineering_proposal,
    safety_production,
    security_check,
    hidden_danger,
    education,
    control,
    emergency,
    special_operation,
    safety_campaign,
  },
  setup() {
    console.log("1-开始创建组件-setup");
    const data = reactive({ sectionId2: null });
    const store = useStore();
    onBeforeMount(() => {
      console.log("2.组件挂载页面之前执行----onBeforeMount");
    });
    onMounted(() => {
      console.log(store.state.sectionId,store.state.projectId,'saidjshdbakjd');
    });
    watch(
      () => [store.state.sectionId],
      ([sectionId]) => {
        data.sectionId2 = sectionId;
      }
    );
    return {
      ...toRefs(data),
    };
  },
};
</script>
<style scoped lang='less'>
#appx {
  display: flex;
  margin-top: .5rem;
  > .centre {
    width: 58.5625rem;
    margin-left: 1rem;
  }
  > .right {
    margin-left: 1rem;
  }
}
.left {
  margin-left: 1rem;
}
</style>