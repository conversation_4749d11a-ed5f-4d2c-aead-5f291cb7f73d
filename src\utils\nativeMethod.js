
// app提供的方法
// 第一版的方法默认都提供了,不做判断,后续方法判断有没有,没有则调用升级接口.
const userAgent = window.navigator.userAgent.toLowerCase();
const isAndroid = userAgent.indexOf("android") > 0;
const isIOS = userAgent.indexOf("iphone") > 0 || userAgent.indexOf("ipad") > 0 || userAgent.indexOf("ipod") > 0 || userAgent.indexOf("ios") > 0;
const isPhone = isAndroid || isIOS;




const exit = (url, back) => {
    const {history} = window;
    history.back();
    if (back === true) {
        if (isAndroid) {
            window.NativeMethod.exit();
        } else if (isIOS) {
            window.webkit.messageHandlers.exit.postMessage(1);
        }
    }
    setTimeout(() => {
        const {hash} = window.location;
        if (hash !== url) {
            return;
        }
        if (isAndroid) {
            window.NativeMethod.exit();
        } else if (isIOS) {
            window.webkit.messageHandlers.exit.postMessage(1);
        }
    }, 100);
};

const open = (url) => {
    if (isAndroid) {
        window.NativeMethod.open(url);
    } else if (isIOS) {
        window.webkit.messageHandlers.open.postMessage(url);
    } else {
        window.open(url);
    }
};

const checkAppUpdate = () => {
    if (isAndroid) {
        window.NativeMethod.checkAppUpdate();
    } else if (isIOS) {
        window.webkit.messageHandlers.checkAppUpdate.postMessage(1);
    }
};

const requestLocationInfo = () => {
    if (isAndroid) {
        window.NativeMethod.requestLocationInfo();
    } else if (isIOS) {
        window.webkit.messageHandlers.requestLocationInfo.postMessage(1);
    }
};

const openMapLocationPage = () => {
    if (isAndroid) {
        window.NativeMethod.openMapLocationPage();
    } else if (isIOS) {
        window.webkit.messageHandlers.openMapLocationPage.postMessage(1);
    }
};

const getStatusBarHeight = () => {
    window.barHeight = window.NativeMethod?.getStatusBarHeight();
    return Number(window.NativeMethod?.getStatusBarHeight() ?? 0);
};

const getBottomSafeHeight = () => {
    if (isAndroid) {
        return 0;
    }
    if (isIOS) {
        return Number(window.NativeMethod.getBottomSafeHeight());
    }
    return 0;
};

const defaultExit = () => {
    if (isAndroid) {
        window.NativeMethod.exit();
    } else if (isIOS) {
        window.webkit.messageHandlers.exit.postMessage(1);
    }
};



const handleException = (val) => {
    if (isAndroid) {
        window.NativeMethod.handleException(val);
    }
    if (isIOS) {
        window.webkit.messageHandlers.handleException.postMessage(val);
    }
};

const nativeMethod = {
    isAndroid,
    isIOS,
    isPhone,
    exit,
    open,
    checkAppUpdate,
    requestLocationInfo,
    getStatusBarHeight,
    handleException,
    openMapLocationPage,
    getBottomSafeHeight,
    defaultExit
};

export default nativeMethod;
