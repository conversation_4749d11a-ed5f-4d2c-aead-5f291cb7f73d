<template>
  <div class="qualityAssessmentSection">
    <div class="qualityAssessmentSection-title"></div>
    <div class="qualityAssessmentSection-content">
      <!-- {{ data }} -->
      <!-- <div class="item" v-for="item in data" :key="item.title" :item="item">{{ item.title }}</div> -->
      <item :item="data[0]"></item>
      <item :item="data[1]"></item>
      <item :item="data[2]"></item>
      <!-- <item class="item" v-for="item in data" :key="item.id" :item="item"></item> -->
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from "vue";
import item from './item.vue'
import api from "@/api/index";
import { useStore } from "vuex";
const store = useStore();
// let data = ref({
//   builderOrgName: "",
//   constructionOrgName: "",
//   costConsultingOrgName: "",
//   designOrgName: "",
//   fullProcessDetectionOrgName: "",
//   supervisionOrgName: "",
// });
const data = ref([
  { id: 1, title:'单位工程', value: { evaluated: 0, evaluationRate: 0, inEvaluation: 0, toEvaluation: 0, total: 0,  } },
  { id: 2, title:'分部工程', value: { evaluated: 0, evaluationRate: 0, inEvaluation: 0, toEvaluation: 0, total: 0 } },
  { id: 3, title:'单元工程', value: { evaluated: 0, evaluationRate: 0, inEvaluation: 0, toEvaluation: 0, total: 0 } },
])

onMounted(() => {
  inspection({
    projectId: store.state.projectId || '',
    sectionId: store.state.sectionId || '',
  });
});
watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    inspection({ projectId: projectId || '', sectionId: sectionId || '' });
  }
);
const inspection = async (params) => {
  if (params.sectionId == null || params.sectionId == '') {
    return
  }
  let res = await api.inspection(params);
  console.log("zss质检评定：", res);
  data.value[0].value = res.danwei;
  data.value[0].value.total = res.danwei.evaluated + res.danwei.inEvaluation + res.danwei.toEvaluation;
  data.value[1].value = res.fenbu;
  data.value[1].value.total = res.fenbu.evaluated + res.fenbu.inEvaluation + res.fenbu.toEvaluation;
  data.value[2].value = res.danyuan;
  data.value[2].value.total = res.danyuan.evaluated + res.danyuan.inEvaluation + res.danyuan.toEvaluation;

};

// onMounted(() => { });

// watch(
//   () => [store.state.sectionId],
//   ([sectionId]) => {
//     if (sectionId != null) {
//       getqualityAssessmentSectionSectionData({ projectId: "", sectionId });
//     }
//   }
// );

// const getqualityAssessmentSectionSectionData = async (params) => {
//   let res = await api.getqualityAssessmentSectionSectionData(params);
//   data.value = res.result;
// };
</script>

<style lang="less" scoped>
.qualityAssessmentSection {
  width: 28.75rem;
  height: 25.25rem;

  .qualityAssessmentSection-title {
    background: url("../../assets/img/zhijianpingdingBox/面板标题.png") center center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .qualityAssessmentSection-content {
    background-color: rgba(46, 96, 185, 0.1);
    // height: 28.5rem;
    width: 100%;

    .item {
      width: 100%;
    }
  }
}
</style>