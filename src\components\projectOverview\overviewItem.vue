<template>
  <div class="content">
    <div class="content-title">
      <div class="title-icon"></div>
      <div class="title-text">{{ item.name }}</div>
    </div>
    <div class="content-content">
      <div class="content-item item1">
        <div class="item-icon icon1"></div>
        <div class="item-text">法人单位：{{ item.legalUnit }}</div>
      </div>
      <div class="content-item item2">
        <div class="item-icon icon2"></div>
        <div class="item-text">资金批复：{{ item.replyInvestmentAmount / 10000 }}亿</div>
      </div>
      <div class="content-item item3">
        <div class="item-icon icon3"></div>
        <div class="item-text">项目负责人：{{ item.projectManager }}</div>
      </div>
      <div class="content-item item4">
        <div class="item-icon icon4"></div>
        <div class="item-text">支付进度：</div>
        <el-progress
          :stroke-width="6"
          :show-text="false"
          :percentage="Number(item.paymentProgress) * 100 || 0"
          :color="'linear-gradient(to right, #023164, #4798F7, #FFFFFF)'"
        />
        <div class="item-progress">{{ (Number(item.paymentProgress) * 100).toFixed(2) }}%</div>
      </div>
      <div class="content-item item5">
        <div class="item-icon icon5"></div>
        <div class="item-text">项目进度：</div>
        <div class="progress progress1" :class="item.designStage == 'finish' ? '' : 'progress1-s'">
            <div class="progress-text">初设报告</div>
        </div>
        <div class="progress progress2" :class="item.bidTenderStage == 'finish' ? '' : 'progress2-s'">
            <div class="progress-text">招标环节</div>
        </div>
        <div class="progress progress2" :class="item.workPrepare == 'finish' ? '' : 'progress2-s'">
            <div class="progress-text">招投标阶段</div>
        </div>
        <div class="progress progress2" :class="item.constructionStage == 'finish' ? '' : 'progress2-s'">
            <div class="progress-text">施工阶段</div>
        </div>
        <div class="progress progress3" :class="item.completionAcceptanceStage == 'finish' ? '' : 'progress3-s'">
            <div class="progress-text">竣工验收</div>
        </div>
      </div>
    </div>
  </div>
</template>
    
    <script setup>
import { onMounted, ref } from "vue";

const props = defineProps({
  item: Object
});
onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>
    
    <style lang="less" scoped>
.content {
  width: 26.75rem;
  height: 9.75rem;
  border: 0.0625rem solid rgba(79, 160, 255, 0.2);
  margin: 0 1rem;
  margin-top: 1rem;

  .content-title {
    width: 100%;
    height: 2.25rem;
    background: linear-gradient(
      to right,
      rgba(71, 152, 247, 0.2),
      rgba(71, 152, 247, 0)
    );
    display: flex;
    align-items: center;
    .title-icon {
      background: url("../../assets/img/projectOverview/icon.png") center center
        no-repeat;
      background-size: 100% 100%;
      width: 1.25rem;
      height: 1.25rem;
      margin-left: 0.8125rem;
    }
    .title-text {
      margin-left: 0.8125rem;
      font-size: 0.875rem;
      font-weight: 700;
      color: #fff;
    }
  }
  .content-content {
    margin: 0.75rem 1rem;
    margin-right: 0.1rem;
    position: relative;
    .content-item {
      display: flex;
      align-items: center;
      .item-icon {
        width: 1rem;
        height: 1rem;
      }
      .item-text {
        margin-left: 0.5rem;
        font-size: 0.75rem;
        color: rgba(211, 222, 236, 1);
      }
      .icon1 {
        background: url("../../assets/img/projectOverview/icon1.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .icon2 {
        background: url("../../assets/img/projectOverview/icon2.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .icon3 {
        background: url("../../assets/img/projectOverview/icon3.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .icon4 {
        background: url("../../assets/img/projectOverview/icon4.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .icon5 {
        background: url("../../assets/img/projectOverview/icon5.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .progress{
        width: 4rem;
        height: 1.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        .progress-text{
            font-size: .75rem;
            color: #1CEEBB;
        }
      }
      .progress1{
        background: url("../../assets/img/projectOverview/progress1.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .progress2{
        background: url("../../assets/img/projectOverview/progress2.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .progress3{
        background: url("../../assets/img/projectOverview/progress3.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .progress1-s{
        background: url("../../assets/img/projectOverview/progress1-s.png") center
          center no-repeat;
        background-size: 100% 100%;
        .progress-text{
            color: #F7C034;
        }
      }
      .progress2-s{
        background: url("../../assets/img/projectOverview/progress2-s.png") center
          center no-repeat;
        background-size: 100% 100%;
        .progress-text{
            color: #F7C034;
        }
      }
      .progress3-s{
        background: url("../../assets/img/projectOverview/progress3-s.png") center
          center no-repeat;
        background-size: 100% 100%;
        .progress-text{
            color: #F7C034;
        }
      }
    }
    .item1 {
      position: absolute;
    }
    .item2 {
      position: absolute;
      top: 1.5rem;
    }
    .item3 {
      position: absolute;
      left: 50%;
      top: 1.5rem;
    }
    .item4 {
      position: absolute;
      top: 3rem;
      display: flex;
      align-items: center;
      :deep(.el-progress) {
        width: 15rem;
        margin-left: .3125rem;
        .el-progress-bar {
          .el-progress-bar__outer {
            background-color: rgba(61, 138, 255, 0.2);
          }
        }
      }
      .item-progress{
        font-size: .875rem;
        font-weight: 500;
        color: #D3DEEC;
        margin-left: .625rem;
      }
    }
    .item5 {
      position: absolute;
      top: 4.5rem;
    }
  }
}
</style>
