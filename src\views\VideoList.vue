<!-- 视频列表 -->
<template>
  <div>
    <navBar Title="视频" isShow="true" @barBack="barBack" />

    <div class="controller">
      <div v-show="videos.length != 0" v-for="(item, index) in videos" :key="item.url">
        <div class="item" @touchstart="touchStartFull(item)" @touchend="touchEndFull">
          <div class="title">{{ item.name }}</div>
          <div class="item-video">
            <!-- <el-image
                  v-show="item.url == null"
                  style="width: calc(96rem ); transform: translateX(10%);  height: calc(57.5rem )"
                  :src="url"
                  :fit="fit"/> -->

            <div class="video" v-show="item.url != null" style="width: calc(96rem );  height: calc(57.5rem )">
              <videoer class="!w-100% !h-100% pointer-events-none" :playUrl="item.url" :idStr="index.toString()"
                :changeFlg="changeFlg">
              </videoer>
              <div class="video-bg"></div>
            </div>
          </div>

          <div class="absolute bottom-1 flex w-full justify-center">
            <span v-for="icon in iconList" :key="icon.name" class="flex items-center"
              @mousedown="controlCamera(icon, item.cameraIndexCode, 0)"
              @mouseup="controlCamera(icon, item.cameraIndexCode, 1)">
              <img :src="getAssetURL(icon.name)" style="width: 32px; height: 32px; margin-top: 2px" />
            </span>
          </div>
        </div>
      </div>
      <div v-show="videos.length == 0" class="full-screen-empty">
        <el-empty :image-size="150" description="暂无视频"></el-empty>
      </div>
    </div>



    <div class="full-screen" v-if="isFullScreen" @touchstart="touchStart" @touchend="touchEnd">
      <div class="full-video">
        <div class="video">
          <videoer :playUrl="fullItem.url" :idStr="1000" :changeFlg="changeFlg">
          </videoer>
          <div class="video-bg"></div>
        </div>
      </div>

      <div class="absolute bottom-1 flex justify-center">
        <span v-for="icon in iconList" :key="icon.name" class="flex items-center"
          @mousedown="controlCamera(icon, fullItem.cameraIndexCode, 0)"
          @mouseup="controlCamera(icon, fullItem.cameraIndexCode, 1)">
          <img :src="getAssetURL(icon.name)" style="width: 32px; height: 32px; margin-top: 2px" />
        </span>
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import api from "@/api/index";
import videoer from "../components/videoer/videoer.vue";
import navBar from "@/components/Header/header.vue";
const videos = ref([]);
const changeFlg = ref(false);
const isFullScreen = ref(false);
const fullItem = ref({ url: '' })
const startTime = ref('')
const lastTime = ref('')
const router = useRoute();
const playUrl = ref("ws://*************:559/openUrl/upb6l5m");
const iconList = ref([
  {
    type: "UP",
    icon: "../assets/img/video_top.png",
    name: "上",
  },
  {
    type: "DOWN",
    icon: "../assets/img/video_bottom.png",
    name: "下",
  },
  {
    type: "LEFT",
    icon: "../assets/img/video_left.png",
    name: "左",
  },
  {
    type: "RIGHT",
    icon: "../assets/img/video_right.png",
    name: "右",
  },
]);
const getAssetURL = (name) => {
  if (name == '上') {
    return new URL(`../assets/img/video_top.png`, import.meta.url).href;
  } else if (name == '下') {
    return new URL(`../assets/img/video_bottom.png`, import.meta.url).href;
  } else if (name == '左') {
    return new URL(`../assets/img/video_left.png`, import.meta.url).href;
  } else if (name == '右') {
    return new URL(`../assets/img/video_right.png`, import.meta.url).href;
  }
  return new URL(image, import.meta.url).href;
};

const controlCamera = (icon, code, type) => {
  let params = {
    cameraIndexCode: code,
    action: type,
    command: icon.type,
  };
  api.videoControl(params).then((res) => {
    console.log(res);
  });
};

onMounted(() => {
  console.log("获取到的参数", router.query);
  //videoList({ projectId: router.query.projectId, sectionId: router.query.sectionId });
  //console.log(store.state.sectionId,store.state.projectId,'saidjshdbakjd');
  //videoList({ projectId: "", sectionId: "" });
  videoList(router.query);
});
const videoList = async (params) => {
  let res = await api.videoList(params);
  videos.value = res.data;
  console.log("监控：", videos.value[0]);
  videos.value.map((item) => {
    videoLink(item);
  })

  // let videoList = res.data

  // const promises = [];
  // videoList.map((item) => {
  //   const promise = videoLink(item);
  //   promises.push(promise);
  // })
  // await Promise.all(promises); // 等待所有请求完成
  // videos.value = videoList

};

const videoLink = async (item) => {
  let res = await api.videoLink(item.cameraIndexCode);
  item.url = res.data.url
};
const close = () => {
  console.log("关闭视频监控", router);
  // router.push('/');
  // 判断是否有上一级路由？有则回退：没有则跳转首页
  window.history.state.back ? router.go(-1) : router.push("/");
};

const showFullScreen = () => {
  console.log('全屏', fullItem.value)
  isFullScreen.value = true;
}
const closeFullScreen = () => {
  console.log('关闭全屏')
  isFullScreen.value = false;
  fullItem.value = { url: '' };
}
function touchStart(event) {
  const currentTime = new Date().getTime();
  if (currentTime - startTime.value < 300 && currentTime - startTime.value > 0) {
    // 判断为双击事件
    closeFullScreen()
  }
  startTime.value = new Date().getTime();
}
function touchEnd(event) {

}

function touchStartFull(item) {
  fullItem.value = item

  const currentTime = new Date().getTime();
  if (currentTime - startTime.value < 300 && currentTime - startTime.value > 0) {
    // 判断为双击事件
    showFullScreen()
  }
  startTime.value = new Date().getTime();
}


</script>

<style scoped lang='less'>
.video-bg-t {
  position: absolute;
  left: 0;
  top: 0;
  width: 80%;
  height: 30%;
  background-color: red;
}

.controller {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
}

.item {
  height: 100%;
  display: flex;
  margin: 10px;
  flex-direction: column;
  align-content: center;
  justify-content: center;
  align-items: center;

}

.title {
  font-size: 14px;
  color: #000;
}

.item-video {
  width: 100%;
  height: auto;
  margin-top: 10px;
  margin-bottom: 10px;
  height: auto;
  align-self: center;

  .video {
    position: relative;

    .video-bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(71, 152, 247, 0);
    }
  }
}

.videoer {
  width: 100%;
  height: auto;
}

.imgStyel {
  display: flex;
  flex-direction: row;

}

.full-screen-empty {
  height: 100vh;
  /* 全屏高度 */
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.full-screen-empty .el-empty {
  max-width: 100%;
  /* 最大宽度为全屏宽度 */
}

.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 1);

  .full-video {
    transform: rotate(90deg);
    width: 100vh;
    height: 100vw;

    .video {
      width: 100vh;
      height: 100vw;

      .video-bg {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(71, 152, 247, 0);
      }
    }
  }

  .justify-center {
    position: absolute;
    transform: rotate(90deg);
    left: 0;
  }
}
</style>