<template>
    <div class="fixed-title-container" :style="`height: ${statusBarHeight}px;`">
        <div  v-if="isShow" @click="handleBack">
          <img name="rect-left" src="../../assets/img/topbar_back_normal.png" class="iconStyle" />
        </div>
        <div class="text">{{ Title }}</div>
    </div>
  </template>
   
  <script setup>
  import {onMounted, ref, toRefs } from "vue";
  import Taro, { getCurrentInstance } from "@tarojs/taro";
  import { useRouter, useRoute } from "vue-router";
  const props = defineProps({
    Title: String,
    isShow: {
      required: false,
      default: false,
    },
  });
  const statusBarHeight = ref(48);
  const router = useRouter();
  const { Title, isShow } = toRefs(props);
  const userAgent = window.navigator.userAgent.toLowerCase();
  const isAndroid = userAgent.indexOf("android") > 0;
  const isIOS = userAgent.indexOf("iphone") > 0 || userAgent.indexOf("ipad") > 0 || userAgent.indexOf("ipod") > 0 || userAgent.indexOf("ios") > 0;
  const isPhone = isAndroid || isIOS;
  const exit = (url, back) => {
    const {history} = window;
    history.back();
    if (back === true) {
        if (isAndroid) {
            window.NativeMethod.exit();
        } else if (isIOS) {
            window.webkit.messageHandlers.exit.postMessage(1);
        }
    }
    setTimeout(() => {
        const {hash} = window.location;
        if (hash !== url) {
            return;
        }
        if (isAndroid) {
            window.NativeMethod.exit();
        } else if (isIOS) {
            window.webkit.messageHandlers.exit.postMessage(1);
        }
    }, 100);
};
  const handleBack=()=>{
   
    // Taro.navigateBack({
    //   delta: 1,
    // })
   // window.history.state.back ? router.go(-1) : router.push("/");
    if(window.history.state.back){
      router.go(-1)
    }else{
     const {hash} = window.location;
        if (exit !== undefined) {
            exit(hash);
        }
    //  alert("点击了最后一页")
    }

  };

  const setStausColor=(color)=>{
      // 设置Web页面的头部颜色
      document.querySelector('.header').style.backgroundColor = color;
 
      // 设置沉浸式header效果
      const el = document.querySelector('meta[name="theme-color"]');
      if (!el) {
        // 如果没有meta标签，则创建一个
        const meta = document.createElement('meta');
        meta.setAttribute('name', 'theme-color');
        meta.setAttribute('content', color);
        document.head.appendChild(meta);
      } else {
        // 如果已经有meta标签，则更新它的值
        el.setAttribute('content', color);
      }
  };

  onMounted(() => {
    statusBarHeight.value = window.statusBarHeight || window.screen.availTop || 48;
    try {
      setStausColor('#000'); // 设置header的背景颜色
        const res = Taro.getSystemInfoSync();
        statusBarHeight.value = res.statusBarHeight;
        console.log('状态栏：',res.statusBarHeight)
      } catch (e) {
        console.log('e',e)
      }
});


  </script>
   
  <style lang="scss">
  .fixed-title-container {
    height: 48px;
    padding: 0 10px;
    width: 100%;
    background: linear-gradient(
      to top right,
      #2833A6,
      #6359D4,
      #6359D4
    );
    left: 0;
    right: 0;
    top: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    .text {
        font-size: 16px;
        color: #fff;
        font-weight: 400;
      }
      
  }
  .iconStyle{
    width: 28px;
    height: 28px;
    padding-top: 6px;
    align-self: center;
  }
  </style>