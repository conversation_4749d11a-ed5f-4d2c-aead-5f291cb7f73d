<template>
  <div class="anquanjianchaBox">
    <div class="title-box">
      <img src="../../assets/img/anquanjianchaBox/title.png" alt="" />
    </div>
    <div class="neirong-box">
      <div class="neirong-item">
        <img src="../../assets/img/anquanjianchaBox/编组 23.png" alt="" />
        <p>
          检查任务：<span>{{ checkData.checkCount }}</span>
        </p>
      </div>
      <div class="neirong-item">
        <img src="../../assets/img/anquanjianchaBox/编组 23-2.png" alt="" />
        <p>
          检查计划：<span>{{ checkData.checkPlanCount }}</span>
        </p>
      </div>
      <div class="neirong-item">
        <img src="../../assets/img/anquanjianchaBox/编组 23-3.png" alt="" />
        <p>
          质量缺陷：<span>{{ checkData.hiddenTroubleCount }}</span>
        </p>
      </div>
      <div class="neirong-item">
        <img src="../../assets/img/anquanjianchaBox/编组 23-4.png" alt="" />
        <p>
          检查内容项：<span>{{ checkData.problemCount }}</span>
        </p>
      </div>
      <div class="neirong-item">
        <img src="../../assets/img/anquanjianchaBox/编组 23-5.png" alt="" />
        <p>
          计划执行率：<span
            >{{ (checkData.planExecuteRate * 100).toFixed(2) }} <span class="span1">%</span></span
          >
        </p>
      </div>
      <div class="neirong-item">
        <img src="../../assets/img/anquanjianchaBox/编组 23-6.png" alt="" />
        <p>
          违规行为：<span>{{ checkData.violationCount }}</span>
        </p>
      </div>
    </div>
  </div>
</template>
  
  <script>
import { reactive, toRefs, onBeforeMount, onMounted, watch } from "vue";
import api from "@/api/index.js";
import { useStore } from "vuex";
export default {
  name: "",
  setup() {
    console.log("1-开始创建组件-setup");
    const store = useStore();
    const data = reactive({
      checkData: {
        checkCount: 0,
        checkPlanCount: 0,
        hiddenTroubleCount: 0,
        planExecuteRate: 0,
        problemCount: 0,
        violationCount: 0,
      },
    });
    onBeforeMount(() => {
      console.log("2.组件挂载页面之前执行----onBeforeMount");
    });
    onMounted(() => {
      console.log("3.-组件挂载到页面之后执行-------onMounted");
      check({ projectId: store.state.projectId, sectionId: store.state.sectionId });
    });
    watch(
      () => [store.state.projectId, store.state.sectionId],
      ([projectId,sectionId]) => {
        check({ projectId, sectionId });
      }
    );
    const check = async (params) => {
      let res = await api.check("QUALITY", params);
      data.checkData = res.data;
    };
    return {
      ...toRefs(data),
    };
  },
};
</script>
<style scoped lang='less'>
.anquanjianchaBox {
  margin-top: 1rem;
  width: 100%;
  height: 100%;
  .title-box {
    width: 100%;
    height: 2rem;
    text-align: left;
    img {
      width: 28.75rem;
      height: 2rem;
    }
  }
  .neirong-box {
    height: 9rem;
    width: 100%;
    background: #2e60b91a;
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    margin-top: 0.3125rem;
    .neirong-item {
      width: 13.25rem;
      height: 3rem;
      display: flex;
      margin: 0.75rem 1.625rem 1rem 1.625rem;
      img {
        height: 3rem;
        margin: 0 0.5rem 0 0;
      }
      p {
        line-height: 3rem;
        font-size: .875rem;
      }
      span {
        font-weight: 700;
        font-size: 1.125rem;
      }
      .span1 {
        font-size: .75rem;
      }
    }
  }
}
</style>