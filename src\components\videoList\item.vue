<template>
  <div class="content">
    <div class="content-item-bg">
      <div class="content-item-bg-title">
        <div class="title-left">
          <div class="left-icon"></div>
          <div class="left-title">
            {{ item.name }}
          </div>
        </div>
        <div class="title-right" @click="fullScreen">
          <div class="right-icon"></div>
          <div class="right-title">放大</div>
        </div>
      </div>
      <!-- <el-image v-show="item.url == null" src="image" fit="contain" /> -->
      <div class="video" v-show="item.url != null">
        <videoer
          style="width: 100%; height: 100%"
          class="!w-100% !h-100% pointer-events-none"
          :playUrl="item.url"
          :idStr="index + 'id'"
          :changeFlg="changeFlg"
        >
        </videoer>
      </div>
    </div>
    <el-dialog
      v-model="isFullScreen"
      :title="item.name"
      width="100vw"
      fullscreen
      :center="false"
      :before-close="
        () => {
          isFullScreen = false;
        }
      "
      modal-class="fullScreen"
      custom-class="fullScreen"
    >
      <!-- <el-image v-show="item.url == null" src="image" fit="contain" /> -->
      <div
        class="video-big"
        style="width: 100%; height: calc(67.5rem - 2.5rem)"
      >
        <videoer
          style="width: 100%; height: 100%"
          class="!w-100% !h-100% pointer-events-none"
          :playUrl="item.url"
          :idStr="index.toString() + 'big'"
          :changeFlg="changeFlg"
        >
        </videoer>
        <div class="absolute bottom-1 flex w-full justify-center" style="margin-top: -40px; position: absolute;">
          <span
            v-for="icon in iconList"
            :key="icon.name"
            class="flex items-center"
            @mousedown="controlCamera(icon, item.cameraIndexCode, 0)"
            @mouseup="controlCamera(icon, item.cameraIndexCode, 1)"
          >
            <img
              class="w-5 h-5"
              :src="getAssetURL(icon.name)"
              style="width: 32px; height: 32px; margin-top: 2px"
            />
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";
import videoer from "../videoer/videoer.vue";
import api from "@/api/index";

const props = defineProps({
  item: Object,
  index: Number,
});
const changeFlg = ref(false);
const isFullScreen = ref(false);

const iconList = ref([
{
    type: "ZOOM_OUT",
    icon: "../../assets/img/video_minus.png",
    name: "缩小",
  },
  {
    type: "ZOOM_IN",
    icon: "../../assets/img/video_add.png",
    name: "放大",
  },
{
    type: "LEFT_UP",
    icon: "../../assets/img/video_left_top.png",
    name: "左上",
  },
  {
    type: "LEFT_DOWN",
    icon: "../../assets/img/video_left_bottom.png",
    name: "左下",
  },
  {
    type: "UP",
    icon: "../../assets/img/video_top.png",
    name: "上",
  },
  {
    type: "DOWN",
    icon: "../../assets/img/video_bottom.png",
    name: "下",
  },
  {
    type: "LEFT",
    icon: "../../assets/img/video_left.png",
    name: "左",
  },
  {
    type: "RIGHT",
    icon: "../../assets/img/video_right.png",
    name: "右",
  },
  {
    type: "RIGHT_UP",
    icon: "../../assets/img/video_right_top.png",
    name: "右上",
  },
  {
    type: "RIGHT_DOWN",
    icon: "../../assets/img/video_right_bottom.png",
    name: "右下",
  },
]);

const getAssetURL = (name) => {
  if (name == '上') {
    return new URL(`../../assets/img/video_top.png`, import.meta.url).href;
  }else if (name == '下') {
    return new URL(`../../assets/img/video_bottom.png`, import.meta.url).href;
  } else if (name == '左') {
    return new URL(`../../assets/img/video_left.png`, import.meta.url).href;
  } else if (name == '右') {
    return new URL(`../../assets/img/video_right.png`, import.meta.url).href;
  }else if (name == '左上') {
    return new URL(`../../assets/img/video_left_top.png`, import.meta.url).href;
  }else if (name == '左下') {
    return new URL(`../../assets/img/video_left_bottom.png`, import.meta.url).href;
  }else if (name == '右上') {
    return new URL(`../../assets/img/video_right_top.png`, import.meta.url).href;
  }else if (name == '右下') {
    return new URL(`../../assets/img/video_right_bottom.png`, import.meta.url).href;
  } else if (name == '缩小') {
    return new URL(`../../assets/img/video_minus.png`, import.meta.url).href;
  } else if (name == '放大') {
    return new URL(`../../assets/img/video_add.png`, import.meta.url).href;
  }
  return new URL(image, import.meta.url).href;
  // return new URL(`../assets/img/video_top.png`, import.meta.url).href;
  // return new URL(`../assets/img/${image}`, import.meta.url).href;
};

const controlCamera = (icon, code, type) => {
  let params = {
    cameraIndexCode: code,
    action: type,
    command: icon.type,
  };
  api.videoControl(params).then((res) => {
    console.log(res);
  });
};

const fullScreen = () => {
  console.log("点击了放大");
  isFullScreen.value = true;
};

onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>

<style lang="less" scoped>
.content {
  .content-item-bg {
    border: 0.0625rem rgba(79, 160, 255, 0.2) solid;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;

    .content-item-bg-title {
      width: 100%;
      height: 2rem;
      display: flex;
      align-items: center;
      position: relative;
      background: linear-gradient(
        to right,
        rgba(71, 152, 247, 0.2),
        rgba(71, 152, 247, 0)
      );
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-left {
        display: flex;
        align-items: center;

        .left-icon {
          background: url("../../assets/img/videoList/icon.png") center center
            no-repeat;
          background-size: 100% 100%;
          width: 1.25rem;
          height: 1.25rem;
          margin-left: 1rem;
        }

        .left-title {
          margin-left: 0.75rem;
          font-size: 0.875rem;
          font-weight: 400;
        }
      }

      .title-right {
        display: flex;
        align-items: center;
        cursor: pointer;

        .right-icon {
          background: url("../../assets/img/videoList/full.png") center center
            no-repeat;
          background-size: 100% 100%;
          width: 1rem;
          height: 1rem;
        }

        .right-title {
          margin-left: 0.5rem;
          font-size: 0.875rem;
          font-weight: 400;
          color: #3dffff;
          margin-right: 1rem;
        }
      }
    }

    .el-image {
      height: 100%;
      margin: 0.5625rem;
    }
    .video {
      height: calc(100% - 2rem);
      margin: 0.5625rem;
    }
  }
}

.el-overlay {
  .el-overlay-dialog {
    .el-dialog {
      background-color: #3dffff;
    }
  }
}

.fullScreen {
  background-color: #000741;
  .el-dialog {
    background-color: #000741;
  }
}

.el-dialog {
  background-color: #3dffff;
}
</style>