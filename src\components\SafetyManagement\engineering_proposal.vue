<template>
  <div class="projectOverview">
    <div class="projectOverview-title">
      <!-- <div class="top-title">选择标段：</div>
      <el-cascader v-model="value" :options="options" @change="handleChange" /> -->
    </div>
    <div class="projectOverview-content">
      <div class="change-0" v-if="changex == 0">
        <div class="top">
          <div class="left">
            <img src="../../assets/img/SafetyManagement/Frame <EMAIL>" alt="" />
            <div class="liability_statement">
              <p class="text2">危大工程：</p>
              <span>{{ tableData.length }}</span>
            </div>
          </div>
          <div class="right">
            <img src="../../assets/img/SafetyManagement/Group <EMAIL>" alt="" />
            <div class="management_system">
              <span class="text2">方案完成率：</span>
              <span>{{ (fanwc * 100).toFixed(2) }}%</span>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="form">
            <div class="table__header">
              <div class="text2">{{ store.getters.isProjectScreen == true ? '标段' : '项目' }}名称</div>
              <div class="text3">危大工程名称</div>
              <div class="text4">类型</div>
              <div class="text4">计划开始</div>
              <div class="text4">计划结束</div>
              <div class="text5">
                <span>方案情况</span>
                <img src="../../assets/img/SafetyManagement/问号@2x.png" alt="" />
              </div>
            </div>
            <div class="table-tr">
              <div class="item" v-for="(item, index) in tableData" :key="index">
                <el-tooltip effect="dark" :content="item.sectionName" placement="top">
                  <el-text class="te2" truncated>
                    {{ item.sectionName }}
                  </el-text>
                </el-tooltip>
                <el-tooltip effect="dark" :content="item.title" placement="top">
                  <el-text class="te3" truncated>
                    {{ item.title }}
                  </el-text>
                </el-tooltip>
                <el-tooltip effect="dark" :content="item.type" placement="top">
                  <el-text class="te4" truncated> 
                    {{ item.type }}
                  </el-text>
                </el-tooltip>
                <el-text class="te6" truncated>
                  {{ formattedDate(item.planStartTime) }}
                </el-text>
                <el-text class="te7" truncated>
                  {{ formattedDate(item.planEndTime) }}
                </el-text>
                <div class="te5" :class="{
        'color-a': item.programFlag == 1,
        'color-b': item.programFlag == 0,
      }">
                  <span v-if="item.programFlag == 0">未完成</span>
                  <span v-else>已完成</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="change-1" v-if="changex == 1">
        <div class="top">
          <div class="left">
            <img src="../../assets/img/SafetyManagement/Frame <EMAIL>" alt="" />
            <div class="liability_statement">
              <p class="text2">危大工程：</p>
              <span>{{ tableData2.length }}</span>
            </div>
          </div>
          <div class="right">
            <img src="../../assets/img/SafetyManagement/Group <EMAIL>" alt="" />
            <div class="management_system">
              <span class="text2">方案完成率：</span>
              <span>{{ (fanwc * 100).toFixed(2) }}%</span>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="form">
            <div class="table__header">
              <div class="text1">危大工程名称</div>
              <div class="text2">类型</div>
              <div class="text3">计划开始</div>
              <div class="text4">计划结束</div>
              <div class="text5">
                <span>方案情况</span>
                <img src="../../assets/img/SafetyManagement/问号@2x.png" alt="" />
              </div>
            </div>
            <div class="table-tr">
              <div class="item" v-for="(item, index) in tableData2" :key="index">
                <div class="te1">{{ item.title }}</div>
                <el-tooltip effect="dark" :content="item.type" placement="top">
                <el-text class="te2" truncated>
                  {{ item.type }}
                </el-text>
                </el-tooltip>
                <div class="te3">
                  {{ item.planStartTime }}
                </div>
                <div class="te4">
                  {{ item.planEndTime }}
                </div>
                <div class="te5" :class="{
        'color-a': item.programFlag == 1,
        'color-b': item.programFlag == 0,
      }">
                  <span v-if="item.programFlag == 0">未完成</span>
                  <span v-else>已完成</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
let store = useStore();
const tableData = ref([]);
const tableData2 = ref([]);
onMounted(() => {
  // if (!store.state.projectId && !store.state.sectionId) {
  dangerProjectx();
  // }
});
const props = defineProps({
  item: Object, // 假设 item 是包含 planStartTime 和 planEndTime 的对象
});
// 监听
let changex = ref(store.getters.isSectionScreen == true ? 1 : 0);
watch(
  () => [store.state.projectId, store.state.sectionId],
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      changex.value = 0;
      dangerProjectx();
    }
    if (store.state.sectionId) {
      changex.value = 1;
      dangerProjectx();
    }
  }
);
// 工程方案数据
let fanwc = ref(0);
const dangerProjectx = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };

  api
    .dangerProject(params)
    .then((res) => {
      let data = res.data.dangerProjects;
      console.log(res, "工程方案数据");
      if (changex.value == 0) {
        tableData.value = [...data];
        fanwc.value = res.data.completeRate;
      }
      if (changex.value == 1) {
        data.forEach((element) => {
          // 创建一个新的 Date 对象，传入当前 element 的 planStartTime 时间戳
          const date = new Date(element.planStartTime);
          const date2 = new Date(element.planEndTime);

          // 格式化日期为 YYYY-M-D 格式
          const formattedDate = `${date.getFullYear()}-${date.getMonth() + 1
            }-${date.getDate()}`;

          // 将格式化后的日期字符串重新赋值给 planStartTime
          element.planStartTime = formattedDate;
          element.planEndTime = formattedDate;

          // 输出重新赋值后的日期字符串
        });
        tableData2.value = [...data];
        fanwc.value = res.data.completeRate;
      }
    })
    .catch((err) => {
      console.log(err, "工程方案数据请求失败");
    });
};
const value = ref([]);
const options = [
  {
    value: "guide",
    label: "Guide",
    children: [
      {
        value: "disciplines",
        label: "Disciplines",
        children: [
          {
            value: "consistency",
            label: "Consistency",
          },
          {
            value: "feedback",
            label: "Feedback",
          },
          {
            value: "efficiency",
            label: "Efficiency",
          },
          {
            value: "controllability",
            label: "Controllability",
          },
        ],
      },
      {
        value: "navigation",
        label: "Navigation",
        children: [
          {
            value: "side nav",
            label: "Side Navigation",
          },
          {
            value: "top nav",
            label: "Top Navigation",
          },
        ],
      },
    ],
  },
  {
    value: "component",
    label: "Component",
    children: [
      {
        value: "basic",
        label: "Basic",
        children: [
          {
            value: "layout",
            label: "Layout",
          },
          {
            value: "color",
            label: "Color",
          },
          {
            value: "typography",
            label: "Typography",
          },
          {
            value: "icon",
            label: "Icon",
          },
          {
            value: "button",
            label: "Button",
          },
        ],
      },
      {
        value: "form",
        label: "Form",
        children: [
          {
            value: "radio",
            label: "Radio",
          },
          {
            value: "checkbox",
            label: "Checkbox",
          },
          {
            value: "input",
            label: "Input",
          },
          {
            value: "input-number",
            label: "InputNumber",
          },
          {
            value: "select",
            label: "Select",
          },
          {
            value: "cascader",
            label: "Cascader",
          },
          {
            value: "switch",
            label: "Switch",
          },
          {
            value: "slider",
            label: "Slider",
          },
          {
            value: "time-picker",
            label: "TimePicker",
          },
          {
            value: "date-picker",
            label: "DatePicker",
          },
          {
            value: "datetime-picker",
            label: "DateTimePicker",
          },
          {
            value: "upload",
            label: "Upload",
          },
          {
            value: "rate",
            label: "Rate",
          },
          {
            value: "form",
            label: "Form",
          },
        ],
      },
      {
        value: "data",
        label: "Data",
        children: [
          {
            value: "table",
            label: "Table",
          },
          {
            value: "tag",
            label: "Tag",
          },
          {
            value: "progress",
            label: "Progress",
          },
          {
            value: "tree",
            label: "Tree",
          },
          {
            value: "pagination",
            label: "Pagination",
          },
          {
            value: "badge",
            label: "Badge",
          },
        ],
      },
      {
        value: "notice",
        label: "Notice",
        children: [
          {
            value: "alert",
            label: "Alert",
          },
          {
            value: "loading",
            label: "Loading",
          },
          {
            value: "message",
            label: "Message",
          },
          {
            value: "message-box",
            label: "MessageBox",
          },
          {
            value: "notification",
            label: "Notification",
          },
        ],
      },
      {
        value: "navigation",
        label: "Navigation",
        children: [
          {
            value: "menu",
            label: "Menu",
          },
          {
            value: "tabs",
            label: "Tabs",
          },
          {
            value: "breadcrumb",
            label: "Breadcrumb",
          },
          {
            value: "dropdown",
            label: "Dropdown",
          },
          {
            value: "steps",
            label: "Steps",
          },
        ],
      },
      {
        value: "others",
        label: "Others",
        children: [
          {
            value: "dialog",
            label: "Dialog",
          },
          {
            value: "tooltip",
            label: "Tooltip",
          },
          {
            value: "popover",
            label: "Popover",
          },
          {
            value: "card",
            label: "Card",
          },
          {
            value: "carousel",
            label: "Carousel",
          },
          {
            value: "collapse",
            label: "Collapse",
          },
        ],
      },
    ],
  },
  {
    value: "resource",
    label: "Resource",
    children: [
      {
        value: "axure",
        label: "Axure Components",
      },
      {
        value: "sketch",
        label: "Sketch Templates",
      },
      {
        value: "docs",
        label: "Design Documentation",
      },
    ],
  },
];

function formattedDate(timestamp) {
  // 创建一个 Date 对象
  const date = new Date(timestamp); // 注意：时间戳通常是毫秒级的，所以需要乘以 1000

  // 格式化日期
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份是从 0 开始的，所以需要 +1
  const day = String(date.getDate()).padStart(2, "0");
  // const hours = String(date.getHours()).padStart(2, "0");
  // const minutes = String(date.getMinutes()).padStart(2, "0");
  // const seconds = String(date.getSeconds()).padStart(2, "0");

  // 组合日期字符串
  return `${year}-${month}-${day}`;
}

</script>

<style lang="less" scoped>
.projectOverview {
  width: 28.75rem;
  // height: 38.75rem;

  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/工程方案面板标题@2x.png") center center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    .top-title {
      font-size: 0.875rem;
      color: #fff;
    }

    :deep(.el-cascader, .el-tooltip__trigge, .el-tooltip__trigger) {

      // background-color: red;
      .el-input__wrapper {
        background-color: rgba(0, 0, 0, 0.1);
        box-shadow: 0 0 0 0;
      }
    }
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);

    width: 100%;
    margin-top: 0.3125rem;
    //   position: absolute;

    >.change-0 {
      height: 15.9375rem;
      padding: 1rem;
      box-sizing: border-box;

      >.top {
        width: 100%;
        display: flex;
        justify-content: space-evenly;

        >.left {
          display: flex;
          align-items: center;

          >img {
            width: 3rem;
          }

          .liability_statement {
            margin-left: 0.625rem;
            display: flex;
            align-items: center;

            .text2 {
              font-weight: 400;
              font-size: 0.875rem;
              color: #e2f2fb;
            }

            >span {
              font-weight: 700;
              font-size: 1.25rem;
            }
          }
        }

        >.right {
          display: flex;
          align-items: center;

          >img {
            width: 3rem;
          }

          >.management_system {
            margin-left: 0.625rem;
            display: flex;
            align-items: center;

            .text2 {
              font-weight: 400;
              font-size: 0.875rem;
              color: #e2f2fb;
            }

            >span {
              font-weight: 700;
              font-size: 1.25rem;
            }
          }
        }
      }

      >.bottom {
        margin-top: 0.625rem;
        width: 100%;
        height: 10rem;

        .form {
          width: 100%;

          .table__header {
            width: 100%;
            height: 2rem;
            background-color: rgba(44, 164, 255, 0.322);
            display: flex;

            .text1 {
              width: 5.5rem;
              height: 2rem;
              text-align: left;
              color: #2ca3ff;
              padding-left: 0.625rem;
              box-sizing: border-box;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text2 {
              width: 4.5rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text3 {
              width: 5.8rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text4 {
              width: 4.5rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text5 {
              width: 5.25rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
              display: flex;
              align-items: center;

              >img {
                width: 1rem;
              }
            }
          }

          .table-tr {
            width: 100%;
            height: 8rem;
            overflow: hidden;
            /* 超出部分隐藏 */
            overflow-y: auto;

            /* 垂直方向滚动条 */
            .item {
              width: 100%;
              border-bottom: 0.0625rem solid rgba(44, 164, 255, 0.322);
              display: flex;
              color: #fff;
              >.te1 {
                width: 5.5rem;
                height: 2rem;
                text-align: left;
                color: #fff;
                padding-left: 0.625rem;
                box-sizing: border-box;
                line-height: 2rem;
                font-size: 0.875rem;
                overflow: hidden;
                font-weight: 400;
              }

              >.te2 {
                width: 4.5rem;
                height: 2rem;
                color: #fff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
                font-weight: 400;
              }

              >.te3 {
                width: 5.8rem;
                height: 2rem;
                  color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
                overflow: hidden;
              }

              >.te4 {
                width: 4.5rem;
                height: 2rem;
                  color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }

              >.te5 {
                width: 5.25rem;
                height: 2rem;
                  color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }

              >.te6 {
                width: 5.25rem;
                height: 2rem;
                color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }

              >.te7 {
                width: 5.25rem;
                height: 2rem;
                  color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
                margin-left: 0.5rem;
              }
            }
          }
        }
      }
    }

    >.change-1 {
      height: 23.75rem;
      padding: 1rem;
      box-sizing: border-box;

      >.top {
        width: 100%;
        display: flex;
        justify-content: space-evenly;

        >.left {
          display: flex;
          align-items: center;

          >img {
            width: 3rem;
          }

          .liability_statement {
            margin-left: 0.625rem;
            display: flex;
            align-items: center;

            .text2 {
              font-weight: 400;
              font-size: 0.875rem;
              color: #e2f2fb;
            }

            >span {
              font-weight: 700;
              font-size: 1.25rem;
            }
          }
        }

        >.right {
          display: flex;
          align-items: center;

          >img {
            width: 3rem;
          }

          >.management_system {
            margin-left: 0.625rem;
            display: flex;
            align-items: center;

            .text2 {
              font-weight: 400;
              font-size: 0.875rem;
              color: #e2f2fb;
            }

            >span {
              font-weight: 700;
              font-size: 1.25rem;
            }
          }
        }
      }

      >.bottom {
        margin-top: 0.625rem;
        width: 100%;
        height: 10rem;

        .form {
          width: 100%;

          .table__header {
            width: 100%;
            height: 2rem;
            background-color: rgba(44, 164, 255, 0.322);
            display: flex;

            .text1 {
              width: 6.25rem;
              height: 2rem;
              text-align: left;
              color: #2ca3ff;
              padding-left: 0.625rem;
              box-sizing: border-box;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text2 {
              width: 4.5rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text3 {
              width: 5.375rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text4 {
              width: 5.375rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text5 {
              width: 5.25rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
              display: flex;
              align-items: center;

              >img {
                width: 1rem;
              }
            }
          }

          .table-tr {
            width: 100%;
            height: 16rem;
            overflow: hidden;
            /* 超出部分隐藏 */
            overflow-y: auto;

            /* 垂直方向滚动条 */
            .item {
              width: 100%;
              border-bottom: 0.0625rem solid rgba(44, 164, 255, 0.322);
              display: flex;

              >.te1 {
                width: 6.25rem;
                height: 2rem;
                text-align: left;
                color: #d3deec;
                padding-left: 0.625rem;
                box-sizing: border-box;
                line-height: 2rem;
                font-size: 0.875rem;
                overflow: hidden;
                font-weight: 400;
              }

              >.te2 {
                width: 4.5rem;
                height: 2rem;
                color: #d3deec;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
                font-weight: 400;
              }

              >.te3 {
                width: 5.375rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
                overflow: hidden;
              }

              >.te4 {
                width: 5.375rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }

              >.te5 {
                width: 5.25rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }
            }
          }
        }
      }
    }
  }
}

.color-a {
  color: #3dffff;
}

.color-b {
  color: #ff5046;
}
</style>