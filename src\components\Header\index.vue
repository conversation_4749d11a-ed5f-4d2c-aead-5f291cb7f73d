<template>
    <view>
      <view
        class="nav-bar"
        :style="{
          paddingTop: statusBarHeight + 'px',
          background: 'linear-gradient(to right, #2833A6, #6359D4)'
        }"
      >
        <view class="nav-bar-layout">
          <view
            class="nav-bar-layout-left"
            @tap="clickEvent()"
            hoverClass="hover"
          >
            <image class="nav-bar-layout-left-icon" :src="topLeftIcon"></image>
          </view>
          <view
            class="nav-bar-layout-title"
            :style="{ color: titleColor, fontSize: titleSize + 'px' }"
            >{{ title }}</view
          >
        </view>
        <view class="nav-bar-line" v-show="!isTop || mode == 0"></view>
      </view>
      <view
        v-if="navData.showTopFill"
        class="nav-bar-null-view"
        :style="{ height: topHeight + 'px' }"
      ></view>
      <slot></slot>
    </view>
  </template>
  
  <script >
  import Taro, { getCurrentInstance } from "@tarojs/taro";

  export default {
    name: "nav-bar",
    props: {
      title: {
        type: String,
        default: "",
      },
      titleSize: {
        type: Number,
        default: 16,
      },
      mode: {
        type: Number,
        default: 0,
      },
      scrollTop: {
        type: Number,
        default: 0,
      },
      changePosition: {
        type: Number,
        default: 10,
      },
      controlBack: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        statusBarHeight: 20,
        topHeight: 64,
        isTop: true,
        navData: null,
        modeData: {
          0: {
            //顶部固定
            titleColorTop: "#ffffff",
            titleColorScroll: "#ffffff",
            topLeftIconTop: "../../assets/img/topbar_back_normal.png",//返回的白色箭头
            topLeftIconScroll: "../../assets/img/topbar_back_normal.png",//返回的黑色箭头
            showTopFill: true,
          }
        },
      };
    },
    watch: {
      mode: {
        handler(newVal, oldVal) {
          try {
            if (this.modeData[newVal]) {
              this.navData = this.modeData[newVal];
            }
          } catch (e) {}
        },
        immediate: true,
      },
      scrollTop: {
        handler(newVal, oldVal) {
          if (newVal > this.changePosition && this.isTop) {
            this.isTop = false;
          }
          if (newVal <= this.changePosition && !this.isTop) {
            this.isTop = true;
          }
          // console.log("==scrollTop==", newVal);
        },
        immediate: true,
      },
    },
    computed: {
      topLeftIcon() {
        return this.isTop
          ? this.navData.topLeftIconTop
          : this.navData.topLeftIconScroll;
      },

      titleColor() {
        return this.isTop
          ? this.navData.titleColorTop
          : this.navData.titleColorScroll;
      },
    },
    destroyed() {},
    mounted() {
      try {
        const res = Taro.getSystemInfoSync();
        this.statusBarHeight = res.statusBarHeight;
        this.topHeight = 44 + res.statusBarHeight;
      } catch (e) {}
    },
    methods: {
      clickEvent() {
        if (this.controlBack) {
          this.$emit("barBack", true);
        } else {
          Taro.navigateBack({});
        }
      },
    },
  };
  </script>
  
  <style lang="scss">
  .nav-bar {
    z-index: 9999;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  
    .nav-bar-layout {
      width: 375px;
      height: 44px;
      position: relative;
  
      .nav-bar-layout-left {
        z-index: 10;
        box-sizing: border-box;
        position: absolute;
        top: 0px;
        left: 0px;
        padding: 10px 0px 10px 16px;
  
        .nav-bar-layout-left-icon {
          width: 24px;
          height: 24px;
        }
      }
  
      .nav-bar-layout-title {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        font-weight: 550;
        font-size: 16px;
        line-height: 22px;
        text-align: center;
        color: #2a2b2e;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  
    .nav-bar-line {
      width: 100%;
      height: 0.25px;
      background-color: #f0f0f0;
      box-shadow: 0px 0.5px 0px rgba(0, 0, 0, 0.12);
    }
  }
  .nav-bar-null-view {
    width: 375px;
  }
  </style>