<template>
  <div class="home">
    <img alt="Vue logo" src="../assets/logo.png" />
    <div @click="test">点击</div>
    <div @click="mzk">mzk</div>
  </div>
</template>

<script>
// @ is an alias to /src
import HelloWorld from "@/components/HelloWorld.vue";
import api from "@/api";

export default {
  name: "HomeView",
  components: {
    HelloWorld,
  },

  setup() {
    const test = async () => {
      console.log("z");

      let data = await api.test();
      console.log(data);
    };

    const mzk = async () => {
      let data = await api.mzk();
      console.log(data);
    };

    return {
      test,mzk
    };
  },
};
</script>

<style lang="less" scoped>
.home {
  background-color: #d7c0c0;
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  z-index: -1;
}
</style>