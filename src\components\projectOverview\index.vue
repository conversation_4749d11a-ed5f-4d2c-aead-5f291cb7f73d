<template>
    <div class="projectOverview">
      <div class="projectOverview-title"></div>
      <vue3-seamless-scroll class="projectOverview-content" :list="dataList" :hover="true" :direction="down" :wheel="true" :step="0.5">
        <overviewItem v-for="(item, index) in dataList" :key="index" :item="item"></overviewItem>
      </vue3-seamless-scroll>
    </div>
  </template>
  
  
  <script setup>
  import { onMounted, ref } from "vue";
  import overviewItem from '../projectOverview/overviewItem.vue'
  import api from '../../api/index.js'

  let dataList = ref([])
  onMounted(() => {
    getProjectStatisticsData( { projectId:'', sectionId:'' } )
  });

  const getProjectStatisticsData = async (params) => {
      let res = await api.getProjectStatisticsData(params)
      console.log('滚动数据：',res.result)
      dataList.value = res.result
  }

  </script>
  
  <style lang="less" scoped>
  .projectOverview {
    width: 28.75rem;
    height: 38.75rem;
  
    .projectOverview-title {
      background: url("../../assets/img/projectOverview/title.png") center center
        no-repeat;
      background-size: 100% 100%;
      height: 2.25rem;
      width: 100%;
    }
  
    .projectOverview-content {
      background-color: rgba(46, 96, 185, 0.1);
      height: 36.25rem;
      width: 100%;
      position: absolute;
      overflow: hidden;
      // overflow-y: auto;
    }
  }
  </style>