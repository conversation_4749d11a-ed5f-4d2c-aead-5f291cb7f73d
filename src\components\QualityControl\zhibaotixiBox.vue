<template>
  <div class="zhibaotixiBox">
    <div class="title-box"></div>
    <div class="neirong-box">
      <div class="neirong-top">
        <div class="zherenshu">
          <img src="../../assets/img/zhibaotixiBox/编组 4备份 2.png" alt="" />
          <div class="text-box">
            <p class="p-text1">12.63<span>%</span></p>
            <p class="p-text2">责任书签订完成率</p>
          </div>
        </div>
        <div class="faunlizhidu">
          <img src="../../assets/img/zhibaotixiBox/编组 4备份 3.png" alt="" />
          <div class="text-box">
            <p class="p-text1">12.63<span>%</span></p>
            <p class="p-text2">管理制度建设完成率</p>
          </div>
        </div>
      </div>
      <div class="xiangmubiao">
        <div class="biaotou">
          <p class="text-name">项目名称</p>
          <p class="text-qianding">责任书签订</p>
          <p class="text-guanli">管理制度建设</p>
        </div>
        <div class="liebiao">
          <p class="text-name">这里是很长很长很长很长项目名称</p>
          <p class="text-qianding">已完成</p>
          <p class="text-guanli">已完成</p>
        </div>
        <div class="liebiao">
          <p class="text-name">这里是很长很长很长很长项目名称</p>
          <p class="text-qianding">已完成</p>
          <p class="text-guanli">已完成</p>
        </div>
        <div class="liebiao">
          <p class="text-name">这里是很长很长很长很长项目名..</p>
          <p class="text-qianding color1">未完成</p>
          <p class="text-guanli color1">未完成</p>
        </div>
        <div class="liebiao">
          <p class="text-name">这里是很长很长很长很长项目名称</p>
          <p class="text-qianding">已完成</p>
          <p class="text-guanli">已完成</p>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script>
import { reactive, toRefs, onBeforeMount, onMounted } from "vue";
export default {
  name: "",
  setup() {
    console.log("1-开始创建组件-setup");
    const data = reactive({});
    onBeforeMount(() => {
      console.log("2.组件挂载页面之前执行----onBeforeMount");
    });
    onMounted(() => {
      console.log("3.-组件挂载到页面之后执行-------onMounted");
    });
    return {
      ...toRefs(data),
    };
  },
};
</script>
  <style scoped lang='less'>
.zhibaotixiBox {
  width: 100%;
  height: 100%;
  .title-box {
    background: url("../../assets/img/zhibaotixiBox/面板标题.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }
  .neirong-box {
    width: 100%;
    box-sizing: border-box;
    padding: 1rem;
    height: 15.9375rem;
    background: #2e60b91a;

    .neirong-top {
      height: 3rem;
      width: 100%;
      display: flex;
      justify-content: space-around;
      .zherenshu,
      .faunlizhidu {
        display: flex;
        margin: 0 .75rem;
        img {
          width: 3rem;
        }
        .text-box {
          width: 6rem;
          height: 3rem;
          text-align: left;
          .p-text1 {
            font-size: 1.125rem;
            color: #fff;
            span {
              font-size: .75rem;
            }
          }
          .p-text2 {
            font-size: .75rem;
          }
        }
      }
      .faunlizhidu {
        .text-box {
          width: 6.75rem;
        }
      }
    }
    .xiangmubiao {
      width: 100%;
      height: 10rem;
      font-size: .875rem;
      margin: .5rem 0;
      .biaotou,
      .liebiao {
        width: 100%;
        height: 2rem;
        display: flex;
        p {
          height: 2rem;
          line-height: 2rem;
          background: #2ca3ff33;
          color: #2ca3ff;
          font-weight: 700;
        }
        .text-name {
          width: 13.75rem;
          padding: 0 0 0 .5rem;
          text-align: left;
        }
        .text-qianding,
        .text-guanli {
          width: 6.25rem;
          text-align: center;
        }
      }
      .liebiao {
        border-bottom: .0625rem solid #4798f766;
        p {
          background-color: #2ca4ff00;
          font-weight: 100;
        }
        .text-name {
          color: #fff;
        }
        .text-qianding,
        .text-guanli {
          color: #3dffff;
        }
        .color1 {
          color: #ff5046;
        }
      }
    }
  }
}
</style>