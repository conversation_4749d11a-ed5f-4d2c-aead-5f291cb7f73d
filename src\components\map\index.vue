<template>
  <div class="content">
    <el-popover v-for="item in dots" :key="item">
      <template #reference>
        <slot name="reference">
          <dot
            :class="item.class"
            :style="`position: absolute; left: ${item.left}rem; top: ${item.top}rem;`"
          ></dot>
        </slot>
      </template>
      <template #default>
        <div>
          <tip
            class="tipView"
            :title="item.name"
            :projectId="item.id"
          ></tip>
        </div>
      </template>
    </el-popover>

    <!-- <tip
      class="tipView"
      v-show="isShowTip"
      :style="tipHid"
      title="宁夏贺兰山防洪治理工程"
    ></tip> -->
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import dot from "./dot.vue";
import tip from "./tip.vue";
const props = defineProps({
  title: String,
});
// const dots = ref([
//   "dot1",
//   "dot2",
//   "dot3",
//   "dot4",
//   "dot5",
//   "dot6",
//   "dot7",
//   "dot8",
// ]);

const dots = ref([]);
const dots2 = ref([
  // {
  //   class: "dot101",
  //   left: "2.5",
  //   top: "2.5",
  //   name: "zss测试",
  //   id: "5bead67ab1f84ecabdf2d8b23b1961e4",
  //   type: 2,
  //   parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   deptCode: "879678967",
  //   proviceCoordinate: null,
  //   cityCoordinate: null,
  //   countyCoordinate: null,
  //   countyId: null,
  //   cityId: null,
  //   cityName: null,
  //   provinceId: null,
  //   longitude: null,
  //   latitude: null,
  //   sortOrder: 148,
  //   deptDataType: 2,
  //   status: 0,
  // },
  // {
  //   class: "dot102",
  //   left: "2.5",
  //   top: "4.5",
  //   name: "etst",
  //   id: "5ec8dffbc1de40ffbd4d13799d852edb",
  //   type: 2,
  //   parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   deptCode: "123123",
  //   proviceCoordinate: null,
  //   cityCoordinate: null,
  //   countyCoordinate: null,
  //   countyId: null,
  //   cityId: null,
  //   cityName: null,
  //   provinceId: null,
  //   longitude: null,
  //   latitude: null,
  //   sortOrder: 147,
  //   deptDataType: 2,
  //   status: 0,
  // },
  // {
  //   class: "dot103",
  //   left: "2.5",
  //   top: "6.5",
  //   name: "大开心新建项目",
  //   id: "dc6d0e6591884a958175aaa3a62bb1ab",
  //   type: 2,
  //   parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   deptCode: "lhhtb20240420",
  //   proviceCoordinate: null,
  //   cityCoordinate: null,
  //   countyCoordinate: null,
  //   countyId: null,
  //   cityId: null,
  //   cityName: null,
  //   provinceId: null,
  //   longitude: null,
  //   latitude: null,
  //   sortOrder: 146,
  //   deptDataType: 2,
  //   status: 0,
  // },
  // {
  //   class: "dot104",
  //   left: "2.5",
  //   top: "8.5",
  //   name: "预付款测试项目",
  //   id: "891ec17f64e8497494cb61d22e5deeb3",
  //   type: 2,
  //   parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   deptCode: "111",
  //   proviceCoordinate: null,
  //   cityCoordinate: null,
  //   countyCoordinate: null,
  //   countyId: null,
  //   cityId: null,
  //   cityName: null,
  //   provinceId: null,
  //   longitude: null,
  //   latitude: null,
  //   sortOrder: 145,
  //   deptDataType: 2,
  //   status: 0,
  // },
  // {
  //   class: "dot105",
  //   left: "2.5",
  //   top: "10.5",
  //   name: "测试项目56",
  //   id: "852d5ca842d24f19bcb8558346bc5377",
  //   type: 2,
  //   parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   deptCode: "56",
  //   proviceCoordinate: null,
  //   cityCoordinate: null,
  //   countyCoordinate: null,
  //   countyId: null,
  //   cityId: null,
  //   cityName: null,
  //   provinceId: null,
  //   longitude: null,
  //   latitude: null,
  //   sortOrder: 144,
  //   deptDataType: 2,
  //   status: 0,
  // },
  {
    class: "dot1",
    left: "12.5",
    top: "27.5",
    name: "宁夏固海扬水灌区续建配套与现代化改造工程",
    id: "d156fdb755874c928b3bf09681be47fa",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "E6401000087000182",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 144,
    deptDataType: 2,
    status: 1,
  },
  {
    class: "dot2",
    name: "固海扩灌扬水更新改造工程",
    id: "47ef1531d8f443889cfff661b7803727",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "1",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 143,
    deptDataType: 2,
    status: 0,
  },
  {
    class: "dot3",
    name: "西吉供水",
    id: "f9021f4c4f6640028de6bdcd0aa806e7",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "3213233132",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 142,
    deptDataType: 2,
    status: 0,
  },
  // {
  //   class: "dot4",
  //   name: "测试测试",
  //   id: "0e898d4239f94f30a5d210224e1c5b44",
  //   type: 2,
  //   parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   deptCode: "333",
  //   proviceCoordinate: null,
  //   cityCoordinate: null,
  //   countyCoordinate: null,
  //   countyId: null,
  //   cityId: null,
  //   cityName: null,
  //   provinceId: null,
  //   longitude: null,
  //   latitude: null,
  //   sortOrder: 141,
  //   deptDataType: 2,
  //   status: 0,
  // },
  {
    class: "dot5",
    name: "黄河宁夏段河道治理工程",
    id: "42704bbdfbfa40f2ba56561e5c9d50e0",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "878797546",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 140,
    deptDataType: 2,
    status: 0,
  },
  {
    class: "dot6",
    name: "红寺堡扬水支干渠支泵站改造工程",
    id: "d83b15a2c18c4415baa02de6aac9e840",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "1324565546465",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 139,
    deptDataType: 2,
    status: 0,
  },
  {
    class: "dot7",
    name: "沙坡头灌区七星渠灌域续建配套与现代化改造工程",
    id: "e541fdcf69b8418b9191092ff7b56599",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "2310-640000-19-01-704967",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 137,
    deptDataType: 2,
    status: 1,
  },
  {
    class: "dot8",
    name: "宁夏回族自治区水利工程建设中心",
    id: "ebd7df75f28b4a938d87458edc04df09",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: null,
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 134,
    deptDataType: 0,
    status: null,
  },
  {
    class: "dot9",
    left: "22.5",
    top: "10.5",
    name: "宁夏贺兰山东麓防洪治理工程",
    id: "6ffdd791a5de446a8265bc7c4a0e1d7a",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "XXXXXX",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 133,
    deptDataType: 2,
    status: 1,
  },
  // {
  //   "class": 'dot10',
  //   "left": "22.5",
  //   "top": "4.5",
  //   "name": "宁夏项目测试专用713",
  //   "id": "84eccc182f2344e9b4ac600dc3ae393e",
  //   "type": 2,
  //   "parentId": "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   "deptCode": "测试项目003",
  //   "proviceCoordinate": null,
  //   "cityCoordinate": null,
  //   "countyCoordinate": null,
  //   "countyId": null,
  //   "cityId": null,
  //   "cityName": null,
  //   "provinceId": null,
  //   "longitude": null,
  //   "latitude": null,
  //   "sortOrder": 130,
  //   "deptDataType": 2,
  //   "status": 1
  // },
  // {
  //   "class": 'dot11',
  //   "left": "24.5",
  //   "top": "3.5",
  //   "name": "金山泵站测试项目",
  //   "id": "5eee929e502b48db92b37ae1ab45c94c",
  //   "type": 2,
  //   "parentId": "71bfe5c651624fa496fc99dab6688f9b",
  //   "deptCode": "JSCS",
  //   "proviceCoordinate": null,
  //   "cityCoordinate": null,
  //   "countyCoordinate": null,
  //   "countyId": null,
  //   "cityId": null,
  //   "cityName": null,
  //   "provinceId": null,
  //   "longitude": null,
  //   "latitude": null,
  //   "sortOrder": 127,
  //   "deptDataType": 2,
  //   "status": 1
  // },
  {
    class: "dot12",
    left: "20.5",
    top: "7.5",
    name: "宁夏青铜峡灌区续建配套与现代化改造工程贺兰金山泵站、林场泵站项目",
    id: "b76298b969204c30af5cc45f8c450532",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "01",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 124,
    deptDataType: 2,
    status: 1,
  },
  // {
  //   "class": 'dot13',
  //   "left": "18.5",
  //   "top": "15.5",
  //   "name": "基建测试项目",
  //   "id": "0295817fffa443ac97364e6dc3b9e8d9",
  //   "type": 2,
  //   "parentId": "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   "deptCode": "测试项目002",
  //   "proviceCoordinate": null,
  //   "cityCoordinate": null,
  //   "countyCoordinate": null,
  //   "countyId": null,
  //   "cityId": null,
  //   "cityName": null,
  //   "provinceId": null,
  //   "longitude": null,
  //   "latitude": null,
  //   "sortOrder": 121,
  //   "deptDataType": 2,
  //   "status": 0
  // },
  // {
  //   "class": 'dot14',
  //   "left": "18.5",
  //   "top": "15.5",
  //   "name": "测试",
  //   "id": "33188fb03b61407288be64e496dc3d83",
  //   "type": 2,
  //   "parentId": "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   "deptCode": "PD-5aSsuh8aj2Xyg",
  //   "proviceCoordinate": "106.278179,38.46637",
  //   "cityCoordinate": "106.278179,38.46637",
  //   "countyCoordinate": "106.28872,38.47392",
  //   "countyId": 640104,
  //   "cityId": 640100,
  //   "cityName": "银川市",
  //   "provinceId": 640000,
  //   "longitude": "",
  //   "latitude": "",
  //   "sortOrder": 121,
  //   "deptDataType": 1,
  //   "status": 0
  // },
  // {
  //   "class": 'dot15',
  //   "name": "宁夏测试公司",
  //   "id": "71bfe5c651624fa496fc99dab6688f9b",
  //   "type": 1,
  //   "parentId": "0afbbfda2c814d25851fa386a7657f10",
  //   "deptCode": null,
  //   "proviceCoordinate": null,
  //   "cityCoordinate": null,
  //   "countyCoordinate": null,
  //   "countyId": null,
  //   "cityId": null,
  //   "cityName": null,
  //   "provinceId": null,
  //   "longitude": null,
  //   "latitude": null,
  //   "sortOrder": 15,
  //   "deptDataType": 0,
  //   "status": null
  // },
  {
    class: "dot16",
    left: "18.5",
    top: "15.5",
    name: "固海扩灌扬水更新改造工程11",
    id: "6f5d72773ea643bbb36390057cf7c71b",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "E6401000087000167",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 8,
    deptDataType: 2,
    status: 1,
  },
  {
    class: "dot17",
    left: "24.5",
    top: "3.5",
    name: "宁夏青铜峡灌区续建配套与现代化改造工程惠农渠灌域（二期）",
    id: "d90d0284f8974617a74f38f127e3528c",
    type: 2,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: "HNQEQ",
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 5,
    deptDataType: 2,
    status: 1,
  },
  // {
  //   class: "dot18",
  //   left: "22.5",
  //   top: "4.5",
  //   name: "软件测试项目长名称",
  //   id: "391e371480a84e548fa0c3002ef2ef0a",
  //   type: 2,
  //   parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
  //   deptCode: "测试项目001",
  //   proviceCoordinate: null,
  //   cityCoordinate: null,
  //   countyCoordinate: null,
  //   countyId: null,
  //   cityId: null,
  //   cityName: null,
  //   provinceId: null,
  //   longitude: null,
  //   latitude: null,
  //   sortOrder: 2,
  //   deptDataType: 2,
  //   status: 1,
  // },
  {
    class: "dot19",
    left: "22.5",
    top: "4.5",
    name: "人员组织架构",
    id: "0afbbfda2c814d25851fa386a7657f10",
    type: 1,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: null,
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 0,
    deptDataType: 0,
    status: null,
  },
  {
    class: "dot20",
    left: "22.5",
    top: "37.5",
    name: "初始化项目部",
    id: "dd3876cb5a004db987a520767e607297",
    type: 2,
    parentId: "0afbbfda2c814d25851fa386a7657f10",
    deptCode: "PD-aYvJYXTuRE7KS",
    proviceCoordinate: "116.405285,39.904989",
    cityCoordinate: "116.405285,39.904989",
    countyCoordinate: "116.41005,39.93157",
    countyId: 110101,
    cityId: 110100,
    cityName: "北京市",
    provinceId: 110000,
    longitude: "",
    latitude: "",
    sortOrder: 0,
    deptDataType: 1,
    status: 0,
  },
  {
    class: "dot21",
    left: "15.5",
    top: "39.5",
    name: "建设管理业务系统",
    id: "e97a85eab07f4f2aa801228a6ba2ea0b",
    type: 1,
    parentId: "e97a85eab07f4f2aa801228a6ba2ea0b",
    deptCode: null,
    proviceCoordinate: null,
    cityCoordinate: null,
    countyCoordinate: null,
    countyId: null,
    cityId: null,
    cityName: null,
    provinceId: null,
    longitude: null,
    latitude: null,
    sortOrder: 0,
    deptDataType: 0,
    status: null,
  },
]);

const dotsClass = ref([
  {
    class: "dot1"
  },
  {
    class: "dot2"
  },
  {
    class: "dot3"
  },
  {
    class: "dot4"
  },
  {
    class: "dot5"
  },
  {
    class: "dot6"
  },
  {
    class: "dot7"
  },
  {
    class: "dot8"
  },
  {
    class: "dot9",
    left: "22.5",
    top: "10.5",
  },
  {
    class: "dot12",
    left: "20.5",
    top: "7.5",
  },
  {
    class: "dot16",
    left: "18.5",
    top: "15.5",
  },
  {
    class: "dot17",
    left: "24.5",
    top: "3.5",
  },
  {
    class: "dot19",
    left: "22.5",
    top: "4.5",
  },
  {
    class: "dot20",
    left: "22.5",
    top: "37.5",
  },
  {
    class: "dot21",
    left: "15.5",
    top: "39.5",
  }
])

onMounted(() => {
  // getProjectList();
  // getProjectCoordinateList()

  Promise.all([getProjectList(), getProjectCoordinateList()]).then(
    ([projectData, addrData]) => {
      // console.log('合并后坐标projectData', projectData, 'addrData', addrData);
      dots.value = []
      for (let item1 of projectData) {
        for (let item2 of addrData) {
          if (item1.id === item2.projectId) {

            let minLeft = 104.420709
            let maxLeft = 107.766046
            let minTop = 39.240753
            let maxTop = 35.368886

            let width = 40;
            let height = 50;

            let leftBili = width / (maxLeft - minLeft);
            let topBili = height / (minTop - maxTop);

            // console.log('得到的坐标点比例：',leftBili,topBili)

            // console.log('得到的坐标点jisuan：',item2.lat,minLeft, leftBili)
            let left = (item2.lon - minLeft) * leftBili;
            let top = (minTop - item2.lat) * topBili;

            // console.log('得到的坐标点：',left,top)

            dots.value.push({ ...item1, ...item2, class: 'dot' + (dots.value.length + 1), left: left, top: top });
          }
        }
      }

      // console.log('合并后坐标数据：', dots);

    }
  );
});

const getProjectList = async () => {
  let res = await api.getProjectList();
  let data = res.data;
  return data;
  let newDots = [];
  for (let index = 0; index < data.length; index++) {
    let item = data[index];
    if (dotsClass.value.length <= index) {
      break;
    }
    let dot = dotsClass.value[index];

    // data[index] = { ...item, ...dot };

    newDots.push({ ...item, ...dot });
  }

  // dots.value = newDots;
  console.log('结果：',dots.value)
};

const getProjectCoordinateList = async () => {
  let res = await api.getProjectCoordinateList();
  let data = res.result;
  return data;
}

</script>

<style lang="less" scoped>
.content {
  width: 37.5rem;
  height: 54.875rem;
  background: url("../../assets/img/map.png") center center no-repeat;
  background-size: 100% 100%;
  position: relative;
  :deep(.el-tooltip__trigger) {
    width: 0.75rem;
    height: 0.75rem;
  }
  .dot1 {
    position: absolute;
    left: 13.5rem;
    top: 27.5rem;
  }
  .dot2 {
    position: absolute;
    left: 10.5rem;
    top: 24.5rem;
  }
  .dot3 {
    position: absolute;
    left: 15.5rem;
    top: 35rem;
  }
  .dot4 {
    position: absolute;
    left: 17rem;
    top: 39.5rem;
  }
  .dot5 {
    position: absolute;
    left: 20.5rem;
    top: 18.5rem;
  }
  .dot6 {
    position: absolute;
    left: 27.5rem;
    top: 22.5rem;
  }
  .dot7 {
    position: absolute;
    left: 22.5rem;
    top: 29.5rem;
  }
  .dot8 {
    position: absolute;
    left: 25.5rem;
    top: 41.5rem;
  }

  //   .ccc{
  //     width: 300px;
  //     height: 300px;
  // background-color: yellow;
  .tooltip-base-box .box-item {
    width: 110px;
    margin-top: 10px;
  }
  //   }
}
</style>