<template>
  <div class="projectOverview">
    <div class="projectOverview-title"></div>
    <div class="projectOverview-content">
      <div class="top">
        <p class="text1">
          {{ txry }}<span class="text2">/{{ zcrs }}</span>
        </p>
        <img class="img1" src="../../assets/img/SafetyManagement/特种人员.png" alt="" />
        <img
          class="img2"
          src="../../assets/img/SafetyManagement/在场人数2.png"
          alt=""
        />
        <!-- <div class="img2">
          <div class="img2-line-top"></div>
          <div class="img2-con"></div>
          <div class="img2-line-bottom"></div>
          <div class="img2-text">人员总数/在场人员</div>
        </div> -->
      </div>
      <div class="bottom">
        <div id="yibiao" style="width: 12.125rem; height: 12.5rem"></div>
        <div class="textimg"></div>
        <div class="img-2"></div>
        <img src="../../assets/img/SafetyManagement/特种证书上传率.png" alt="" />
      </div>
    </div>
  </div>
</template>


<script setup>
import { onMounted, ref, watch } from "vue";
import * as echarts from "echarts";
import api from "@/api/index";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
const route = useRoute();
let store = useStore();
onMounted(() => {
  // if (!store.state.sectionId && !store.state.projectId) {
  campaignx();
  // }
  yibiaob();
});
// 监听
watch(
  () => [store.state.sectionId, store.state.projectId],
  (newVal, oldVal) => {
    // if (newVal !== oldVal) {
    campaignx();
    // }
  }
);
let txry = ref(0);
let zcrs = ref(0);
// 特种人员数据
const campaignx = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };

  api
    .person(params)
    .then((res) => {
      let data = res.data;
      txry.value = data.specialNumber;
      zcrs.value = data.presenceNumber;
      if (route.path == "/safetyManagement") {
        yibiaob(data.uploadRate);
      }
    })
    .catch((err) => {
      console.log(err, "特种人员数据请求失败");
    });
};
// 证书上传率
const yibiaob = async (licensedNumber) => {
  let santjElement = document.getElementById("yibiao");
  // 销毁已有的 echarts 实例
  echarts.dispose(santjElement);

  const yibiao = await echarts.init(document.getElementById("yibiao"));
  const option = ref({
    series: [
      {
        type: "gauge",
        center: ["50%", "50%"],
        // startAngle: 200,
        // endAngle: -20,
        min: 0,
        max: 100,
        splitNumber: 4,
        itemStyle: {
          color: "#3CC4FE",
        },
        progress: {
          show: true,
          width: 10,
        },
        pointer: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            width: 10,
            color: [[1, "#293548"]],
          },
        },
        axisTick: {
          distance: -20,
          splitNumber: 5,
          length: 5,
          lineStyle: {
            width: 2,
            color: "#999",
            fontSize: 12,
          },
        },
        splitLine: {
          distance: -20,
          length: 8,
          lineStyle: {
            width: 3,
            color: "#999",
            fontSize: 12,
          },
        },
        axisLabel: {
          distance: -10,
          color: "#999",
          fontSize: 10,
        },
        anchor: {
          show: false,
        },
        title: {
          show: false,
        },
        detail: {
          valueAnimation: true,
          width: "60%",
          lineHeight: 40,
          borderRadius: 8,
          offsetCenter: [0, 0],
          fontSize: 22,
          fontWeight: "bolder",
          formatter: "{value} %",
          color: "inherit",
          rich: {
            value: {
              fontSize: 36, // 增加字号
              fontWeight: "bold", // 加粗
              textShadow: "1px 1px 5px rgba(0, 0, 0, 0.5)", // 添加文本阴影
            },
          },
        },
        data: [
          {
            value: licensedNumber ? (licensedNumber * 100).toFixed(2) : 0,
            itemStyle: {
              fontSize: 36,
              fontWeight: "bold",
              textShadow: "1px 1px 5px rgba(0, 0, 0, 0.5)",
              color: "#0097DC",
            },
          },
        ],
      },
    ],
  });

  yibiao.setOption(option.value);
};
</script>

<style lang="less" scoped>
.projectOverview {
  width: 28.75rem;
  //   height: 620px;

  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/特殊作业人员统计.png") center center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 15.9rem;
    width: 100%;
    margin-top: .3125rem;
    //   position: absolute;
    padding: 1.6875rem 2.1875rem;
    box-sizing: border-box;
    display: flex;

    >.top {
      width: 50%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      position: relative;

      .img1 {
        position: absolute;
        top: -12%;
        width: 9.5rem;
        // height: 9.375rem;
      }

      .img2 {
        width: 8.4rem;
        height: 2rem;
        position: relative;
        .img2-text{
          position: absolute;
          line-height: 2rem;
        }

        .img2-line-top {
          width: 100%;
          height: .0625rem;
          background: url("../../assets/img/SafetyManagement/line.png") center center no-repeat;
          background-size: 100% 100%;
        }

        .img2-con {
          width: 100%;
          height: calc(100% - 0.125rem);
          background: linear-gradient(90deg, rgba(39, 94, 160, 0) 0%, rgba(39, 94, 160, 0.4) 46.73%, rgba(39, 94, 160, 0) 100%);
 
        }

        .img2-line-bottom {
          width: 100%;
          height: .0625rem;
          background: url("../../assets/img/SafetyManagement/line.png") center center no-repeat;
          background-size: 100% 100%;
        }

      }

      .text1 {
        font-weight: 700;
        font-size: 2.25rem;
        color: #3ce7fe;
        text-shadow: 0 0 1rem #2770ff;

        >.text2 {
          font-size: 1rem;
          font-weight: 700;
        }
      }
    }

    >.bottom {
      position: relative;

      >.textimg {
        position: absolute;
        top: 45%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 7.125rem;
        height: 5.75rem;
        background: url("../../assets/img/SafetyManagement/特种证书上传率-半圆.png") center center no-repeat;
        background-size: 100% 100%;
      }

      >.img-2 {
        position: absolute;
        top: 73%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 6.25rem;
        height: .9375rem;
        background: url("../../assets/img/SafetyManagement/特种证书上传率-方块.png") center center no-repeat;
        background-size: 100% 100%;
      }

      >img {
        width: 8.25rem;
        height: 2rem;
        position: absolute;
        top: 92%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}

.color-a {
  color: #3dffff;
}

.color-b {
  color: #ff5046;
}
</style>