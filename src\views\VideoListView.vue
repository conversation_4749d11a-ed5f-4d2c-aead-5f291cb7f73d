<template>
  <div>
   <navBar
     Title="视频搜索"
     isShow="true"
      @barBack="barBack">
  </navBar>
 
  <div class="controller">
    <div class="search_input">
    <el-input
            v-model="searchKey"
            @input="handleInput"
            placeholder="请输入单位或项目名称"
            clearable/>
  </div>
    <div>
      <el-tree
            class="filter-tree"
            :data="projectList"
            :props="defaultProps"
            :load="loadNode"
            :filter-node-method="filterNode"
            lazy
            ref="treeRef"
            @node-click="handleNodeClick"/>
    </div>
  </div>
 
    
  </div>
</template>

<script setup>
import { ref, onMounted,watch} from "vue";
import { useRouter, useRoute } from "vue-router";
import api from "@/api/index";
import navBar from "@/components/Header/header.vue";
import { ElTree } from 'element-plus';
    const router = useRouter();
    const searchKey = ref("");
    const projectList = ref([]);
    const selectTree = ref(null);
    const defaultProps = {
      children: "children",
      label: "name",
      value: "id",
      isLeaf: "leaf",
    };




onMounted(() => {
  getProjectList();
  //videoList({ projectId: "", sectionId: "" });
});

// 监听filterText==>input绑定的值
watch(searchKey, (newQuery) => {
  if (newQuery) {
    projectList.value =  projectList.value.filter((item) => item.name.includes(newQuery));
  } else {
    getProjectList();
  }
});
const getProjectList = async () => {
  let res = await api.getProjectList();
  console.log(res);
  projectList.value = res.data;
};

const loadNode = async (node, resolve) => {
  console.log(node);
  if (node.level === 0) {
    return resolve([]);
  }
  if (node.level > 1) return resolve([]);

  let res = await api.getSectionList({ projectId: node.data.id });
  res.data.forEach((obj) => {
    obj.leaf = true;
    obj.isSection = true;
  });
  resolve(res.data);
};
const filterNode = (value, data) => {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
};
// const videoList = async (params) => {
//   let res = await api.videoList(params);
//   console.log("监控：", res);
//   videos.value = res.data;
// };

const querySearch = (queryString, cb) => {
  // const results = queryString
  //   ? restaurants.value.filter(createFilter(queryString))
  //   : restaurants.value
  // call callback function to return suggestions
  // cb(results)
  console.log(queryString, cb);
};

const scrollTop=(res)=>{

};
const handleInput = (value) => {
  searchKey.value = value.trim(); // 去除输入的首尾空白字符
};
const barBack=()=>{

}

const handleNodeClick = (data) => {
  console.log("点击：id",  data.projectId);
  if (data.isSection == true) {
    router.push({ path: '/VideoList', query: {projectId: data.projectId, sectionId: data.id } });
   // videoList({ projectId: data.projectId, sectionId: data.id });
  } else {
    router.push({ path: '/VideoList', query: { projectId: data.id, sectionId: "" } });
  }
    
};


</script>


<style scoped lang='less'>

.controller {
  height: 100%;
  width: 100%;
  background-color:#F4F4F4;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

}

.search_input {
        width: 100%;
        :deep(.el-input) {
          margin: 15px;
          width: 92%;
          height: 50px;
          .el-input__wrapper {
            background-color: white;
            box-shadow: none;
            border: 1px solid #E5E5E5;
            border-radius: 5px;

            .el-input__inner {
              color: #000;
              font-size: 16px;
            }
          }
        }
      }
      //整体背景色
    :deep(.el-tree){
      background: #F4F4F4;
      color: #000;
      margin-bottom: 50px;
      font-size: 16PX;
      .el-tree-node__label{
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 350px; /* 设置最大宽度 */
      }
    }


   
</style>