import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import ElementPlus from "element-plus";
import "element-plus/dist/index.css";
import "./utils/rem.js";
import vue3SeamlessScroll from "vue3-seamless-scroll";

// 处理从SSO回调传递的token（查询参数方式）
const urlParams = new URLSearchParams(window.location.search);
const token = urlParams.get("token");
if (token) {
  // 存储token到localStorage
  localStorage.setItem("token", token);
  console.log("Token received from URL params and stored:", token);

  // 清除URL中的token参数，避免敏感信息暴露
  const url = new URL(window.location);
  url.searchParams.delete("token");
  window.history.replaceState(
    {},
    document.title,
    url.pathname + url.search + url.hash
  );
}

createApp(App)
  .use(store)
  .use(router)
  .use(ElementPlus)
  .use(vue3SeamlessScroll)
  .mount("#app");
