<template>
  <div class="jianchayingxiangBox">
    <div class="title-box">
      <img src="../../assets/img/jianchayingxiangBox/面板标题.png" alt="" />
    </div>
    <!-- <div class="qiehuan-img">
      <img
        class="left-img"
        src="../../assets/img/jianchayingxiangBox/Group 6.png"
        alt=""
      />
      <img
        class="right-img"
        src="../../assets/img/jianchayingxiangBox/Group 7.png"
        alt=""
      />
    </div> -->
    <el-carousel :interval="500000" arrow="always" @change="carouselChange">
      <el-carousel-item v-for="item in checkData" :key="item">
        <div class="neirong-box">
          <div class="jibenxinxi">
            <div class="kuai"></div>
            <p>基本信息</p>
          </div>
          <div class="jibenxinxi-box">
            <!-- <p class="text-xixin1">项目名称 ：{{ item.projectName }}</p> -->
            <el-tooltip class="box-item" effect="dark" :content="`项目名称 ：${item.projectName || ''}`" placement="top">
              <el-text class="text-xixin5" truncated>项目名称 ：{{ item.projectName || '' }}</el-text>
            </el-tooltip>
            <!-- <p class="text-xixin2">标段名称 ：{{ item.sectionName }}</p> -->
            <el-tooltip class="box-item" effect="dark" :content="`标段名称 ：${item.sectionName}`" placement="top">
              <el-text class="text-xixin5" truncated>标段名称 ：{{ item.sectionName }}</el-text>
            </el-tooltip>
            <p class="text-xixin3">检查人 ：{{ item.checker }}</p>
            <p class="text-xixin4">
              检查日期 ：{{ item.checkDate == '' ? '' : formattedDate(item.checkDate) }}
            </p>
            <!-- <p class="text-xixin5">
              检查分项 ：{{ item.checkOptions[0].option }}
            </p> -->
            <el-tooltip class="box-item" effect="dark" :content="`检查分项 ：${item.checkOptions[0].option}`"
              placement="top">
              <el-text class="text-xixin5" truncated>检查分项 ：{{ item.checkOptions[0].option }}</el-text>
            </el-tooltip>
            <el-tooltip class="box-item" effect="dark" :content="`检查描述 ：${item.checkOptions[0].description}`"
              placement="top">
              <el-text class="text-xixin6" truncated>检查描述 ：{{ item.checkOptions[0].description }}</el-text>
            </el-tooltip>
            <p class="text-xixin7">
              检查结果 ：{{ result[item.checkOptions[0].result] || '' }}
            </p>
            <p class="text-xixin8">
              问题级别 ：{{ dangerLevel[item.checkOptions[0].dangerLevel] || '' }}
            </p>
          </div>
          <div class="zhenggai-box">
            <div class="jibenxinxi zhenggai">
              <div class="kuai"></div>
              <p>整改前</p>
            </div>
            <div class="jibenxinxi zhenggai">
              <div class="kuai"></div>
              <p>整改后</p>
            </div>
          </div>
          <div class="img-box">
            <div class="img-box-left">
              <!-- <img v-if="item.checkOptions != null &&
                item.checkOptions[0].afterReformPic != null && item.checkOptions[0].afterReformPic[0].url != ''
              " :src="item.checkOptions[0].afterReformPic[0].url" alt="" /> -->
              <el-image style="width: 100%; height: 100%" v-if="item.checkOptions != null &&
                item.checkOptions[0].beforeReformPic != null && item.checkOptions[0].beforeReformPic[0].url != ''
              " :src="item.checkOptions[0].beforeReformPic[0].url" fit="contain" />
              <div v-else class="noImg">暂无图片</div>
            </div>
            <div class="img-box-right">
              <!-- <img v-if="item.checkOptions != null &&
      item.checkOptions[0].beforeReformPic != null && item.checkOptions[0].beforeReformPic[0].url != ''
      " :src="item.checkOptions[0].beforeReformPic[0].url" alt="" /> -->
              <el-image v-if="item.checkOptions != null &&
                item.checkOptions[0].afterReformPic != null && item.checkOptions[0].afterReformPic[0].url != ''
              " :src="item.checkOptions[0].afterReformPic[0].url" fit="contain" />
              <div v-else class="noImg">暂无图片</div>
            </div>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
import { reactive, toRefs, onBeforeMount, onMounted, watch } from "vue";
import api from "@/api/index.js";
import { useStore } from "vuex";
export default {
  // item.checkOptions[0].afterReformPic[0].fileName
  name: "",
  setup() {
    const store = useStore();
    console.log("1-开始创建组件-setup");
    const data = reactive({
      checkData: [],
      result: ["无需整改", "限期整改", "立即整改", "停工整改"],
      dangerLevel: ["违规行为", "严重缺陷"],
    });
    onBeforeMount(() => {
      console.log("2.组件挂载页面之前执行----onBeforeMount");
    });
    onMounted(() => {
      console.log("3.-组件挂载到页面之后执行-------onMounted");
      checkImage({
        projectId: store.state.projectId,
        sectionId: store.state.sectionId,
      });
    });
    watch(
      () => [store.state.projectId, store.state.sectionId],
      ([projectId, sectionId]) => {
        checkImage({ projectId, sectionId });
      }
    );
    const checkImage = async (params) => {
      let res = await api.checkImage(params);
      console.log("检查影响", res);
      data.checkData = res.data;

      if (data.checkData.length > 0) {
        const dataItem = data.checkData[0];
        if (dataItem.checkOptions) {
          const checkItem = dataItem.checkOptions[0];
          if (checkItem.afterReformPic != null) {
            for (const afterPic of checkItem.afterReformPic) {
              getFileList(afterPic);
            }
          }
          if (checkItem.beforeReformPic != null) {
            for (const beforePic of checkItem.beforeReformPic) {
              getFileList(beforePic);
            }
          }
        }
      } else {
        data.checkData = [{
          projectName: '',
          sectionName: '',
          checkDate: '',
          checkOptions: [{ afterReformPic: [{ url: '' }], beforeReformPic: [{ url: '' }], option: '', description: '' }]
        }]
      }

      if (data.checkData.length > 1) {
        const dataItem = data.checkData[1];
        if (dataItem.checkOptions) {
          const checkItem = dataItem.checkOptions[0];
          if (checkItem.afterReformPic != null) {
            for (const afterPic of checkItem.afterReformPic) {
              getFileList(afterPic);
            }
          }
          if (checkItem.beforeReformPic != null) {
            for (const beforePic of checkItem.beforeReformPic) {
              getFileList(beforePic);
            }
          }
        }
      }

      // for (const dataItem of data.checkData) {
      //   for (const checkItem of dataItem.checkOptions) {
      //     if (checkItem.afterReformPic != null) {
      //       for (const afterPic of checkItem.afterReformPic) {
      //         getFileList(afterPic);
      //       }
      //     }
      //     if (checkItem.beforeReformPic != null) {
      //       for (const beforePic of checkItem.beforeReformPic) {
      //         getFileList(beforePic);
      //       }
      //     }
      //   }
      // }
    };
    async function getFileList(item) {
      const imgUrl = await _getImgUrlData({
        fileUUIDList: [item.fileUuid],
        fileType: 0,
      });
      if (Array.isArray(imgUrl) && imgUrl.length > 0) {
        imgUrl.map((listItem) => {
          item.url = listItem.downloadUrls[0];
        });
      }

      console.log("下载完图片：", data.checkData);
    }
    // 根据 uuid 获取图片
    async function _getImgUrlData(params) {
      const imgData = await api.postFileaddressLongLineDownloadURLs(params);
      return imgData;
    }

    function formattedDate(timestamp) {
      // 创建一个 Date 对象
      const date = new Date(timestamp); // 注意：时间戳通常是毫秒级的，所以需要乘以 1000

      // 格式化日期
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份是从 0 开始的，所以需要 +1
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      // 组合日期字符串
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }


    const carouselChange = (i) => {
      const dataItem = data.checkData[i + 1];
      if (dataItem == undefined || dataItem == null) return;
      if (dataItem.checkOptions) {
        const checkItem = dataItem.checkOptions[0];
        if (checkItem.afterReformPic != null) {
          for (const afterPic of checkItem.afterReformPic) {
            if (!afterPic.hasOwnProperty('url')) {
              getFileList(afterPic);
            }
          }
        }
        if (checkItem.beforeReformPic != null) {
          for (const beforePic of checkItem.beforeReformPic) {
            if (!beforePic.hasOwnProperty('url')) {
              getFileList(beforePic);
            }
          }
        }

      }
    }

    return {
      ...toRefs(data),
      formattedDate,
      carouselChange,
    };
  },
};
</script>
<style scoped lang='less'>
.jianchayingxiangBox {
  width: 100%;
  height: 100%;
  font-size: 0.875rem;
  position: relative;
  z-index: 10;

  .qiehuan-img {
    width: 100%;
    height: 0;

    img {
      width: 1rem;
      position: absolute;
      top: 27.25rem;
    }

    .left-img {
      left: 0.875rem;
    }

    .right-img {
      right: 0.375rem;
    }
  }

  .title-box {
    height: 2.25rem;
    width: 100%;
    display: flex;

    img {
      width: 28.75rem;
    }
  }

  :deep(.el-carousel) {
    height: 42.1875rem;
    width: 100%;

    .el-carousel__container {
      height: 100%;
    }

    .el-carousel__arrow {
      background-color: rgba(0, 0, 0, 0.001);

      .el-icon {
        opacity: 1;
      }
    }

    .el-carousel__indicators {
      display: none;
    }

    .el-carousel__arrow--left {
      margin-top: 4.5rem;
      left: -1rem;

      .el-icon {
        svg {
          opacity: 0;
        }

        background: url("../../assets/img/jianchayingxiangBox/Group 6.png") center center no-repeat;
        background-size: 100% 100%;
      }
    }

    .el-carousel__arrow--right {
      margin-top: 4.5rem;
      right: -1rem;

      .el-icon {
        svg {
          opacity: 0;
        }

        background: url("../../assets/img/jianchayingxiangBox/Group 7.png") center center no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .neirong-box {
    height: 42.1875rem;
    width: 100%;
    background: #2e60b91a;
    padding: 1.5rem 2.25rem 1rem;
    box-sizing: border-box;
    margin-top: .3125rem;

    .jibenxinxi {
      height: 1.25rem;
      display: flex;
      margin: 0 0 1rem 0;

      .kuai {
        width: 0.25rem;
        height: 0.75rem;
        background-color: #2ca3ff;
        margin: 0.25rem 0.25rem 0 0;
      }

      p {
        font-weight: 700;
        line-height: 1.25rem;
      }
    }

    .jibenxinxi-box {
      width: 100%;
      display: flex;
      flex-wrap: wrap;

      p {
        text-align: left;
        width: 17.3125rem;
        height: 1.25rem;
        line-height: 1.25rem;
        margin: 0 0.375rem 0.625rem 0;
      }

      .text-xixin6,
      .text-xixin5 {
        text-align: left;
        width: 17.3125rem;
        height: 1.25rem;
        line-height: 1.25rem;
        margin: 0 0.375rem 0.625rem 0;
        font-size: .875rem;
        color: #fff;
      }
    }

    .zhenggai-box {
      height: 1.25rem;
      width: 100%;
      display: flex;
      margin: 1.875rem 0 1rem;

      .zhenggai {
        width: 26rem;

        &:first-child {
          margin: 0 2.25rem 0 0;
        }
      }
    }

    .img-box {
      width: 100%;
      height: 26.9375rem;
      display: flex;

      .img-box-left,
      .img-box-right {
        // display: flex;
        // justify-content: center;
        // align-items: center;
      }

      div {
        width: 26rem;
        height: 100%;
        border: 0.0625rem solid #2ca3ff;

        &:first-child {
          margin: 0 2.25rem 0 0;
        }

        img {
          width: 25rem;
          margin: 0.5rem;
        }

        .noImg {
          line-height: 26rem;
        }
      }
    }
  }
}
</style>