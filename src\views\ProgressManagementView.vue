<!-- app进度管理 -->
 <template>
    <div>
        <navBar
            Title="进度管理"
            isShow="true"
            @barBack="barBack"/> 
            
           <div class="title-top"> 项目进度列表</div>
        
        <div class="contarin">
            <progressItem v-for="(item, index) in dataList" :key="index" :item="item"></progressItem>
        </div>    
    </div>
 </template>

 <script setup>
    import { ref, onMounted} from "vue";
    import { useRouter, useRoute } from "vue-router";
    import api from "@/api/index";
    import navBar from "@/components/Header/header.vue";
    import progressItem from '../components/progressManagement/progressItem.vue';
    let dataList = ref([])
    onMounted(() => {
        getProjectStatisticsData( { projectId:'', sectionId:'' } )
     });
     const getProjectStatisticsData = async (params) => {
      let res = await api.getProjectStatisticsData(params)
      dataList.value = res.result
  }
</script>

<style scoped lang='less'>
    .contarin{
        height: 100%;
        width: 100%;
        display: flex;
        flex-direction: column;
        background-color:white;
        padding-bottom: 15px;
    }
    .title-top{
        font-size: 18px;
        color: black;
        font-weight: 700;
        padding: 10px 20px;
        width: 100%;
        background-color: white;
        display: flex;
        justify-content: flex-start;
    }
</style>