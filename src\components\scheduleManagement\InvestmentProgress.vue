<template>
  <div class="content-item">
    <div v-show="icon == '1'" class="invest-icon"></div>
    <div v-show="icon == '2'" class="invest-icon2"></div>
    <div class="invest-content">
      <div class="invest-content-title">
        <div class="invest-content-title-title">{{ title }}</div>
        <div class="invest-content-title-pro">
          {{ progress }}
          <div class="invest-content-title-pro-b">%</div>
        </div>
      </div>
      <el-progress
        :percentage="Number(progress) || 0"
        :show-text="false"
        :stroke-width="10"
        :color="'linear-gradient(to right, #023164, #3DFFFF, #FFFFFF)'"
      />
    </div>
  </div>
</template>
  
<script setup>
import { onMounted, ref } from "vue";

const props = defineProps({
  title: String,
  progress: String,
  icon: String,
});
onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>
  
<style lang="less" scoped>
.content-item {
  margin: 1rem 1rem 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.invest-icon {
  background: url("../../assets/img/investment/finish_invest.png") center center
    no-repeat;
  background-size: 100% 100%;
  width: 4rem;
  height: 3.375rem;
}
.invest-icon2 {
  background: url("../../assets/img/investment/finish_debt.png") center center
    no-repeat;
  background-size: 100% 100%;
  width: 4rem;
  height: 3.375rem;
}
.invest-content {
  flex-grow: 1;
  margin-left: 0.9375rem;
  .invest-content-title {
    display: flex;
    justify-content: space-between;
    .invest-content-title-title {
      font-size: 0.875rem;
    }
    .invest-content-title-pro {
      font-size: 1.25rem;
      color: #3dffff;
      font-weight: 700;
      display: flex;
      align-items: flex-end;
      .invest-content-title-pro-b {
        font-size: 0.75rem;
        margin-bottom: 0.125rem;
      }
    }
  }
  :deep(.el-progress) {
    border-radius: 0 !important;
    .el-progress-bar {
      .el-progress-bar__outer {
        border-radius: 0;
        background-color: rgba(61, 255, 255, 0.2);
        .el-progress-bar__inner {
          border-radius: 0;
        }
      }
    }
  }
  .el-progress path:first-child {
    stroke: #0896d8;
  }
}
</style>
  