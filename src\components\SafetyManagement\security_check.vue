<template>
  <div class="projectOverview">
    <div class="projectOverview-title"></div>
    <div class="projectOverview-content">
      <div class="sumuo">
        <div class="top-box">
          <div class="box1">
            <img
              src="../../assets/img/SafetyManagement/编组 <EMAIL>"
              alt=""
            />
            <div class="tetx-box">
              <p class="text1">检查任务：</p>
              <p class="text2">{{ listData.checkCount }}</p>
            </div>
          </div>
          <div class="box2">
            <img
              src="../../assets/img/SafetyManagement/检查计划*********"
              alt=""
            />
            <div class="tetx-box">
              <p class="text1">检查计划：</p>
              <p class="text2">{{ listData.checkPlanCount }}</p>
            </div>
          </div>
          <div class="box3">
            <img
              src="../../assets/img/SafetyManagement/违规行为 <EMAIL>"
              alt=""
            />
            <div class="tetx-box">
              <p class="text1">违规行为：</p>
              <p class="text2">{{ listData.violationCount }}</p>
            </div>
          </div>
        </div>
        <div class="bottom-box">
          <div class="box1">
            <img src="../../assets/img/SafetyManagement/问题总数.png" alt="" />
            <div class="tetx-box">
              <p class="text1">检查内容项：</p>
              <p class="text2">{{ listData.problemCount }}</p>
            </div>
          </div>
          <div class="box2">
            <img
              src="../../assets/img/SafetyManagement/计划执行*********"
              alt=""
            />
            <div class="tetx-box">
              <p class="text1">计划完成率：</p>
              <p class="text2">{{ (listData.planExecuteRate * 100).toFixed(2) }}%</p>
            </div>
          </div>
          <div class="box3">
            <img src="../../assets/img/SafetyManagement/安全隐患.png" alt="" />
            <div class="tetx-box">
              <p class="text1">安全隐患：</p>
              <p class="text2">{{ listData.hiddenTroubleCount }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
      
      
<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
let store = useStore();
onMounted(() => {
  // if (!store.state.projectId && !store.state.sectionId) {
    checkexaminex();
  // }
});
// 监听
watch(
  () => [store.state.projectId, store.state.sectionId],
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      checkexaminex();
    }
  }
);
// 安全检查
let listData = ref({
  checkCount: 0, // 检查总数
  checkPlanCount: 0, //检查计划数
  hiddenTroubleCount: 0, //质量缺陷数/安全隐患数
  planExecuteRate: 0, //计划执行率
  problemCount: 0, //问题总数
  violationCount: 0, //违规行为数
});
const checkexaminex = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  api
    .checkexamine(params)
    .then((res) => {
      console.log(res, "安全检查调用成功");
      let data = res.data;
      listData.value = data;
      console.log(listData.value, "查看");
    })
    .catch((err) => {
      console.log(err, "安全检查调用失败");
    });
};
</script>
      
    <style lang="less" scoped>
.projectOverview {
  width: 58.5rem;
  //   height: 38.75rem;

  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/安全检查面板标题@2x.png")
      center center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 28.75rem;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 9rem;
    width: 58.5rem;
    margin-top: 0.3125rem;
    //   position: absolute;
    padding: 0.75rem 1rem;
    box-sizing: border-box;

    .sumuo {
      width: 100%;
      .top-box {
        width: 100%;
        height: 3rem;
        display: flex;
        justify-content: space-between;
        > .box1,
        .box2,
        .box3 {
          display: flex;
          align-items: center;
          width: 14rem;
          height: 3rem;
          > img {
            width: 3rem;
          }
          > .tetx-box {
            height: 1.875rem;
            margin-left: 0.75rem;
            display: flex;
            align-items: center;
            > .text1 {
              font-weight: 400;
              font-size: 0.875rem;
              color: #e2f2fb;
            }
            > .text2 {
              font-size: 1.25rem;
              font-weight: 700;
            }
          }
        }
      }
      .bottom-box {
        margin-top: 1rem;
        width: 100%;
        height: 3rem;
        display: flex;
        justify-content: space-between;
        > .box1,
        .box2,
        .box3 {
          display: flex;
          align-items: center;
          width: 14rem;
          height: 3rem;
          > img {
            width: 3rem;
          }
          > .tetx-box {
            height: 1.875rem;
            margin-left: 0.75rem;
            display: flex;
            align-items: center;
            > .text1 {
              // width: 5.25rem;
              font-weight: 400;
              font-size: 0.875rem;
              color: #e2f2fb;
            }
            > .text2 {
              font-size: 1.25rem;
              font-weight: 700;
            }
          }
        }
      }
    }
  }
}
</style>