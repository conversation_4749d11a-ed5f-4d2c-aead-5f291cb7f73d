// 公共请求方法
import axios from '../utils/request';
const api = {
    async login() {
        return await axios.post('/auth-server/auth/token', {
            loginType: '-1',
            password: '66e2d6e5e7d3ce3b76bae61732bf9702',
            username: 'yt2',
        })
    },

    // 获取项目
    // http://*************:8182/gateway/pdscommon/rs/org/list
    // async getProjectList(params) {
    //     return await axios.get('/pdscommon/rs/org/list', params)
    // },
    
    async getProjectList(params) {
        return await axios.get('/luban-misc/project/list', params)
    },
    
    async getProjectListHasAuth(params) {
        return await axios.get('/pdscommon/rs/org/list', params)
    },
    // 获取项目坐标
    async getProjectCoordinateList(params) {
        return await axios.get('/builder/infra/dept/coordinate', params)
    },

    // 获取标段
    // http://*************:8182/gateway/luban-misc/project/section/list?projectId=b76298b969204c30af5cc45f8c450532
    async getSectionList(params) {
        return await axios.get('/luban-misc/project/section/list/noAuth', params)
    },

    async getSectionListHasAuth(params) {
        return await axios.get('/luban-misc/project/section/list', params)
    },

    // 项目详情
    // http://*************:8182/gateway/builder/infra/dept/detail/deptId/5bead67ab1f84ecabdf2d8b23b1961e4
    async getProjectDetails(projectId) {
        return await axios.get(`/builder/infra/dept/detail/deptId/${projectId}`)
    },

    // 用户的信息
    // http://*************:8182/gateway/auth-server/user/get-user-detail
    async getUserDetails() {
        return await axios.get('/auth-server/user/get-user-detail')
    },

    // 获取 图片 文件上传地址
    async postFileaddressLongLineDownloadURLs(params) {
        return await axios.post('/pdscommon/rs/fileaddress/longLineDownloadURLs', params)
    },

    // 简介
    async getDescription(params) {
        return await axios.post('/builder/command/description', params)
    },

    // 投资计划执行
    async getInvestmentData(params) {
        return await axios.post('/builder/command/investment', params)
    },

    // 参建单位
    async getWorkplaceData(params) {
        return await axios.post('/builder/command/participant', params)
    },

    // 参建单位--标段
    async getWorkplaceSectionData(params) {
        return await axios.post('/builder/command/participant/section', params)
    },

    // 项目总览 
    async getProjectStatisticsData(params) {
        return await axios.post('/builder/command/project', params)
    },

    // 项目统计 
    async getProjectStatisticsCenterData(params) {
        return await axios.post('/builder/command/project/track/center', params)
    },

    // 项目跟踪 
    async getProjectTrackingData(params) {
        return await axios.post('/builder/command/project/track', params)
    },

    // 工程布置图
    async getqualityAssessmentSectionSectionData(params) {
        return await axios.post('/builder/command/layoutDrawings', params)
    },

    // 工程布置图
    async getLayoutDrawings(params) {
        return await axios.post('/builder/command/layoutDrawings', params)
    },

    // 大事记
    async getMajorEvents(params) {
        return await axios.post('/luban-data-manage/command/majorEvents', params)
    },

    // 人员信息 
    async getPersonInfoData(params) {
        return await axios.post('/builder/command/person/info', params)
    },

    // 标段划分
    async getSectionDivisionData(params) {
        return await axios.post('/builder/command/section/divide', params)
    },

    // 安保体系-- 责任书的签订
    async manageRegime(moduleType, params) {
        return await axios.post(`/sphere/command/${moduleType}/responsibility${params.sectionId == null || params.sectionId == '' ? '' : '/section'}`, params)
    },

    // 安保体系-- 管理制度建设
    async manageRegimeSection(moduleType, params) {
        return await axios.post(`/luban-workprepare/command/${moduleType}/manage/regime${params.sectionId == null || params.sectionId == '' ? '' : '/section'}`, params)
    },

    //质量交底 技术交底
    async disclosure(params) {
        return await axios.post('/sphere/command/QUALITY/disclosure', params)
    },

    // 安全会议(项目-中心) 安全活动
    async campaign(params) {
        return await axios.post('/sphere/command/meeting', params)
    },
    // 安全交底(项目-中心) 安全活动
    async securityx(params) {
        return await axios.post('/sphere/command/SECURITY/disclosure', params)
    },

    // 特种人员作业统计
    async person(params) {
        return await axios.post('/sphere/command/special/person', params)
    },

    // 危大工程 工程方案
    async dangerProject(params) {
        return await axios.post('/sphere/command/dangerProject', params)
    },

    //安全教育 
    async education(params) {
        return await axios.post('/sphere/command/education', params)
    },
    // 安全生产投入-计划费用(标段)
    async sectionpian(params) {
        return await axios.post('/sphere/command/investment/plan/section', params)
    },
    // 安全生产投入-支付费用(标段)
    async defrayment(params) {
        return await axios.post('/luban-meter-highway/command/security/cost/section', params)
    },
    // 安全生产投入-计划费用(中心-项目)
    async anqplan(params) {
        return await axios.post('/sphere/command/investment/plan', params)
    },
    // 安全生产投入-支付费用(中心-项目)
    async costplan(params) {
        return await axios.post('/luban-meter-highway/command/security/cost', params)
    },

    // 应急管理--应急演练(中心屏)
    async emergencyDrill(params) {
        return await axios.post('/sphere/command/emergency/drill', params)
    },

    // 应急管理--应急预案(中心屏)
    async emergencyPlan(params) {
        return await axios.post('/luban-workprepare/command/emergency/plan', params)
    },

    // 应急管理--应急演练(标段屏)
    async emergencyDrillSection(params) {
        return await axios.post('/sphere/command/emergency/drill/section', params)
    },

    // 应急管理--应急预案(标段屏)
    async emergencyPlanSection(params) {
        return await axios.post('/luban-workprepare/command/emergency/plan/section', params)
    },

    // 检查影像
    async checkImage(params) {
        return await axios.post('/sphere/command/checkImage', params)
    },

    // 安全检查，质量检查
    async check(moduleType, params) {
        return await axios.post(`/sphere/command/${moduleType}/check`, params)
    },
    // 安全交底(标段)
    async checkSection(params) {
        return await axios.post(`/sphere/command/SECURITY/disclosure/section`, params)
    },
    // 安全会议(标段)
    async safetysection(params) {
        return await axios.post(`/sphere/command/meeting/section`, params)
    },
    // 安全管控 风险等级
    async risk(params) {
        return await axios.post(`/sphere/command/risk`, params)
    },
    // 隐患级别统计 安全隐患统计
    async hiddenTrouble(params) {
        return await axios.post(`/sphere/command/SECURITY/hiddenTrouble`, params)
    },
    // 隐患级别统计 整改趋势
    async rectifyTrend(params) {
        return await axios.post(`/sphere/command/SECURITY/rectifyTrend`, params)
    },
    // 安全-质量检查 安全检查
    async checkexamine(params) {
        return await axios.post(`/sphere/command/SECURITY/check`, params)
    },
    // 安全管控 在场人员统计
    async presence(params) {
        return await axios.post(`/sphere/command/presence`, params)
    },

    // 监控视频
    async videoList(params) {
        return await axios.post(`/sphere/video/camera`, params)
    },

    // 获取单个监控视频链接地址
    async videoLink(cameraIndexCode) {
        return await axios.get(`/sphere/video/getLink/${cameraIndexCode}`)
    },

    // 监控视频--操作
    async videoControl(params) {
        return await axios.post(`/sphere/video/control`, params)
    },

    // 质检评定
    async inspection(params) {
        return await axios.post(`/luban-inspection/command/inspection${params.sectionId == null || params.sectionId == '' ? '' : '/section'}`, params)
    },

    // 质量检测
    async material(params) {
        return await axios.post(`/luban-inspection/command/material/test`, params)
    },
}

export default api
