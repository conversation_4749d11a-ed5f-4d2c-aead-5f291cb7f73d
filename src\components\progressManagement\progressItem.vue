<template>
  <div class="contarin-style">
      <div class="title-text flex-row">{{item.name}}</div>
      <div class="flex-row">
        <div class="item-icon icon1"></div>
        <div class="tag-style">法人单位：</div>
        <div class="title-style">{{item.legalUnit==""?"--":item.legalUnit}}</div>
      </div>
      <div class="flex-row">
        <div class="item-icon icon2"></div>
        <div class="tag-style">资金批复：</div>
        <span class="title-style">{{ item.replyInvestmentAmount / 10000 }}亿</span>
      </div>

      <div class="flex-row">
        <div class="item-icon icon3"></div>
        <div class="tag-style">项目负责人：</div>
        <span class="title-style">{{ item.projectManager }}</span>
      </div>

      <div class="flex-row">
        <div class="item-icon icon4"></div>
        <div class="tag-style">支付进度：</div>
        <el-progress
          :stroke-width="6"
          :show-text="false"
          :type="line"
          :percentage="Number(item.paymentProgress) || 0"
          :color="'linear-gradient(to right, #02A7F0, #02A7F0, #02A7F0)'"
        />
        <div class="title-style" style="margin-left: 5px;">{{ item.paymentProgress==null?"0":item.paymentProgress}}%</div>
      </div>

      <div class="flex-row">
        <div class="item-icon icon5"></div>
        <div class="tag-style">项目进度：</div>
        <div class="progress progress1" :class="item.designStage == 'finish' ? 'progress1-s' : ''">
          <div class="progress-text">初设报告</div>
      </div>
      <div class="progress progress2" :class="item.bidTenderStage == 'finish' ? 'progress2-s' : ''">
          <div class="progress-text">招标环节</div>
      </div>
      <div class="progress progress2" :class="item.workPrepare == 'finish' ? 'progress2-s' : ''">
          <div class="progress-text">开工准备</div>
      </div>
      <div class="progress progress2" :class="item.constructionStage == 'finish' ? 'progress2-s' : ''">
          <div class="progress-text">施工阶段</div>
      </div>
      <div class="progress progress3" :class="item.completionAcceptanceStage == 'finish' ? 'progress3-s' : ''">
          <div class="progress-text">竣工验收</div>
      </div>
      </div>
     
  
  </div>
</template>
    
    <script setup>
import { onMounted, ref } from "vue";

const props = defineProps({
  item: Object
});
onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>
    
<style lang="less" scoped>
.contarin-style {
  width: 86%;
  height: auto;
  background-color: white;
  border: 0.1px solid #999  ;
  border-radius: 10px;
  margin: 10px;
  padding: 10px;
}
.title-text {
  font-size: 16px;
  font-weight: 700;
  width: 90%;
  color: #000;    

}
.flex-row{
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin-top: 10px;
  padding-left: 10px;
  padding-right: 10px;
  align-items: center;
}    
.tag-style{
  font-size: 14px;
  color: #000;
  flex-shrink: 0;
}
.title-style{
  font-size: 16px;
  color: #666;
}
.item-icon {
  width: 24px;
  height: 24px;
  margin-right: 10px
}
.icon1 {
        background: url("../../assets/img/projectOverview/icon1.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .icon2 {
        background: url("../../assets/img/projectOverview/icon2.png") center
          center no-repeat;
        background-size: 100% 100%;
      }      
      .icon3 {
        background: url("../../assets/img/projectOverview/icon3.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .icon4 {
        background: url("../../assets/img/projectOverview/icon4.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .icon5 {
        background: url("../../assets/img/projectOverview/icon5.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
.el-progress{width:60%;}    
.lable-style{
   
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .progress{
        width: 60px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .progress-text{
            font-size: 8px;
            color: #1CEEBB;
        }
      }
      .progress1{
        background: url("../../assets/img/projectOverview/progress1.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .progress2{
        background: url("../../assets/img/projectOverview/progress2.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .progress3{
        background: url("../../assets/img/projectOverview/progress3.png") center
          center no-repeat;
        background-size: 100% 100%;
      }
      .progress1-s{
        background: url("../../assets/img/projectOverview/progress1-s.png") center
          center no-repeat;
        background-size: 100% 100%;
       
      }
      .progress2-s{
        background: url("../../assets/img/projectOverview/progress2-s.png") center
          center no-repeat;
        background-size: 100% 100%;
        .progress-text{
            color: #F7C034;
        }
      }
      .progress3-s{
        background: url("../../assets/img/projectOverview/progress3-s.png") center
          center no-repeat;
        background-size: 100% 100%;
        .progress-text{
            color: #F7C034;
        }
      }


</style>
