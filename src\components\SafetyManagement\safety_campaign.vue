<template>
  <div class="projectOverview">
    <div class="projectOverview-title"></div>
    <div class="projectOverview-content">
      <div class="bottom" v-if="changex == 0">
        <div class="form">
          <!-- <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="date" label="Date" width="180" />
              <el-table-column prop="name" label="Name" width="180" />
              <el-table-column prop="address" label="Address" />
            </el-table> -->
          <div class="table__header">
            <div class="text1">{{ store.getters.isProjectScreen == true ? '标段' : '项目' }}名称</div>
            <div class="text2">安全交底(次)</div>
            <div class="text3">安全会议(次)</div>
          </div>
          <div class="table-tr">
            <div class="item" v-for="(item, index) in tableData" :key="index">
              <div class="te1">{{ item.name }}</div>
              <div class="te2">
                {{ item.number2 }}
              </div>
              <div class="te3">
                {{ item.number }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="change-1" v-if="changex == 1">
        <ul class="list">
          <li class="item1">
            <span class="text1">{{ safety }}</span>
            <span class="text2">次</span>
          </li>
          <li class="item2">
            <img
              src="../../assets/img/SafetyManagement/安全活动-安全交底.png"
              alt=""
            />
          </li>
          <li class="item3">
            <img
              src="../../assets/img/SafetyManagement/安全会议-安全交底2.png"
              alt=""
            />
          </li>
        </ul>
        <ul class="list">
          <li class="item1">
            <span class="text1">{{ safety2 }}</span>
            <span class="text2">次</span>
          </li>
          <li class="item2">
            <img
              src="../../assets/img/SafetyManagement/安全活动-安全会议1.png"
              alt=""
            />
          </li>
          <li class="item3">
            <img
              src="../../assets/img/SafetyManagement/安全活动-安全会议-3.png"
              alt=""
            />
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
    
    
<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
let store = useStore();
onMounted(() => {
  // if (!store.state.projectId  && !store.state.sectionId ) {
    campsec();
  // }
  // if (store.state.projectId || store.state.sectionId) {
  //   campsec();
  // }
  if (store.getters.isSectionScreen == true) {
    checksec();
      safetyse();
  }
});
const props = defineProps({
  moduleType: String,
});
// 监听
let changex = ref(store.getters.isSectionScreen == true ? 1 : 0);
watch(
  () => [store.state.projectId, store.state.sectionId],
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      changex.value = 0;
      campsec();
    }
    if (store.state.sectionId) {
      changex.value = 1;
      checksec();
      safetyse();
    }
  }
);
// 数据合并
const campsec = () => {
  Promise.all([campaignx(), securitya()])
    .then(([anqx, cost]) => {
      const mergedData = anqx.map(value1 => {
        const matchingValue2 = cost.find(value2 => value2.id === value1.id);
        return matchingValue2 ? { ...matchingValue2, ...value1 } : value1;
      });

      tableData.value = mergedData;
    })
    .catch((err) => {
      // 处理任一请求失败的情况
      console.error("Error fetching data:", err);
    });
};

  // Promise.all([campaignx(), securitya()])
  //   .then(([anqx, cost]) => {
  //     for (let value1 of anqx) {
  //       for (let value2 of cost) {
  //         if (value1.id === value2.id) {
  //           tableData.value.push({ ...value1, ...value2 });
  //         }
  //       }
  //     }
  //   })
  //   .catch((err) => {
  //     // 处理任一请求失败的情况
  //     console.error("Error fetching data:", err);
  //   });

// 安全会议(项目-中心)
const campaignx = async () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };

  let data1 = await api.campaign(params);
  console.log("data1xxxxxxxxx", data1);
  return data1.data.statistics;
};
// 安全交底(项目-中心)
const securitya = async () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };

  let data2 = await api.securityx(params);
  let statisticsArray = data2.data.statistics;
  let arr = [];
  statisticsArray.forEach((statistic) => {
    let obj = {
      id: statistic.id,
      name: statistic.name,
      number2: statistic.number,
    };
    arr.push(obj);
  });
  return arr;
};
const tableData = ref([]);

// 安全交底(标段)
let safety = ref(0);
const checksec = async () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };

  let data = await api.checkSection(params);
  safety.value = data.data;
};
// 安全会议(标段)
let safety2 = ref(0);
const safetyse = async () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };

  let data = await api.safetysection(params);
  safety2.value = data.data;
};
</script>
    
  <style lang="less" scoped>
.projectOverview {
  width: 28.75rem;
  //   height: 38.75rem;

  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/安全活动.png") center
      center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 15.9375rem;
    width: 100%;
    margin-top: 0.3125rem;
    padding: 1rem;
    box-sizing: border-box;
    > .bottom {
      width: 100%;
      height: 100%;
      // padding: 0 1.25rem;
      box-sizing: border-box;

      .form {
        width: 100%;

        .table__header {
          width: 100%;
          height: 2rem;
          background-color: rgba(44, 164, 255, 0.322);
          display: flex;
          .text1 {
            width: 14rem;
            height: 2rem;
            text-align: left;
            color: #2ca3ff;
            padding-left: 0.625rem;
            box-sizing: border-box;
            line-height: 2rem;
            font-weight: 700;
            font-size: 0.875rem;
          }
          .text2 {
            width: 6.375rem;
            height: 2rem;
            color: #2ca3ff;
            text-align: center;
            line-height: 2rem;
            font-weight: 700;
            font-size: 0.875rem;
          }

          .text3 {
            width: 6.375rem;
            height: 2rem;
            color: #2ca3ff;
            text-align: center;
            line-height: 2rem;
            font-weight: 700;
            font-size: 0.875rem;
          }
        }

        .table-tr {
          width: 100%;
          height: 12.5625rem;
          overflow: hidden; /* 超出部分隐藏 */
          overflow-y: auto; /* 垂直方向滚动条 */
          .item {
            width: 100%;
            border-bottom: 0.0625rem solid rgba(44, 164, 255, 0.322);
            display: flex;
            > .te1 {
              width: 14rem;
              height: 2rem;
              text-align: left;
              color: #ffffff;
              padding-left: 0.625rem;
              box-sizing: border-box;
              line-height: 2rem;
              font-size: 0.875rem;
              overflow: hidden;
            }
            > .te2 {
              width: 6.375rem;
              height: 2rem;
              //   color: #ffffff;
              text-align: center;
              line-height: 2rem;
              font-size: 0.875rem;
            }
            > .te3 {
              width: 6.375rem;
              height: 2rem;
              //   color: #ffffff;
              color: #d3deec;
              text-align: center;
              line-height: 2rem;
              font-size: 0.875rem;
            }
          }
        }
      }
    }

    > .change-1 {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      > .list {
        width: 8.25rem;
        height: 12.375rem;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        > .item1 {
          > .text1 {
            font-weight: 700;
            font-size: 2.25rem;
            color: #3ce7fe;
            text-shadow: 0 0 16px #2770ff;
          }
          > .text2 {
            font-weight: 500;
            font-size: 0.75rem;
            color: #3ce7fe;
            text-shadow: 0 0 16px #2770ff;
          }
        }
        > .item2 {
          > img {
            width: 6.5rem;
            height: 6.75rem;
          }
        }
        > .item3 {
          > img {
            width: 8.25rem;
            height: 2rem;
          }
        }
      }
    }
  }
}

.color-a {
  color: #3dffff;
}
.color-b {
  color: #ff5046;
}
</style>