<template>
  <div class="content">
    <div class="content-left">
      <div class="left-icon" :style="{'background':`url(${url}) no-repeat center`}"></div>
      <div class="left-text">{{ item.role }}</div>
    </div>
    <div class="content-center">
      <div class="center-name">{{ item.name }}</div>
    </div>
    <div class="content-right">
      <div class="right-phone">{{ item.phone }}</div>
    </div>
  </div>
</template>
        
        <script setup>
import { onMounted, ref } from "vue";

const props = defineProps({
  item: Object
});

let url = ref(require("../../assets/img/personInfo/icon1.png"))

if (props.item.role == "PM") {
  url = ref(require("../../assets/img/personInfo/icon1.png"))
}else if (props.item.role == "QI") {
  url = ref(require("../../assets/img/personInfo/icon2.png"))
}else if (props.item.role == "SO") {
  url = ref(require("../../assets/img/personInfo/icon3.png"))
}else if (props.item.role == "CO") {
  url = ref(require("../../assets/img/personInfo/icon4.png"))
}else if (props.item.role == "DO") {
  url = ref(require("../../assets/img/personInfo/icon5.png"))
}

onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>
        
    <style lang="less" scoped>
.content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .content-left {
    display: flex;
    width: 6rem;
    .left-icon {
      // background: url("../../assets/img/personInfo/icon1.png") center center
      //   no-repeat;
      background-size: 100% 100% !important;
      height: 1rem;
      width: 1rem;
    }
    .left-text {
      margin-left: 0.5rem;
    }
  }
  .content-center {
    .center-name {
      text-align: center;
    }
  }
  .content-right {
    display: flex;
    width: 6rem;
    .right-phone {
    }
  }
}
</style>