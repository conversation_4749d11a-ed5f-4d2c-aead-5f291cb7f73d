#!/bin/bash

DIST_FOLDER="dist"
REMOTE_SERVER="*************"
REMOTE_FOLDER="/root/deploy-dists"
ZIP_FILE_NAME="bigscreen"
DOCKER_CONTAINER="1660-builder-nginx-1"
DOCKER_FOLDER="/luban/pkg/front-end"
SSH_USERNAME="root"

echo "======================Build dist...======================"
rm -rf $DIST_FOLDER
yarn build

echo "======================Zip dist...======================"
cd $DIST_FOLDER
zip -r $ZIP_FILE_NAME.zip *