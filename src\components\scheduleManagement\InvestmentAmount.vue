<template>
  <div class="content">
    <div class="content-item-bg">
      <div class="content-item-bg-line" :class="type == 2 ? 'content-item-bg-line2' : ''"></div>
      <div class="content-item-bg-title">
        <div class="content-item-bg-title-1" :class="type == 2 ? 'content-item-bg-title-1-2' : ''">{{ amount }}</div>
        <div class="content-item-bg-title-2">{{ title }}</div>
      </div>
      <div class="content-item-bg-line" :class="type == 2 ? 'content-item-bg-line2' : ''"></div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref } from "vue";

const props = defineProps({
  title: String,
  amount: Number,
  type: Number,
});
onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>

<style lang="less" scoped>
.content {
  width: 12.875rem;
  height: 4.5rem;
  background: linear-gradient(
    to right,
    rgba(61, 255, 255, 0),
    rgba(61, 255, 255, 0.15),
    rgba(61, 255, 255, 0)
  );
}
.content-item-bg {
  background: url("../../assets/img/investment/itemBg.png") center center
    no-repeat;
  background-size: 100% 100%;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .content-item-bg-line {
    width: 100%;
    height: 0.0938rem;
    background: linear-gradient(
      to right,
      rgba(61, 255, 255, 0),
      rgba(61, 255, 255, 1),
      rgba(61, 255, 255, 0)
    );
  }
  .content-item-bg-line2 {
    background: linear-gradient(
      to right,
      rgba(41, 139, 255, 0),
      rgba(41, 139, 255, 1),
      rgba(41, 139, 255, 0)
    );
  }
  .content-item-bg-title {
    .content-item-bg-title-1 {
      color: #3dffff;
      font-size: 1.5rem;
      font-weight: 700;
    }
    .content-item-bg-title-1-2 {
      color: #3CC4FE;
    }
    .content-item-bg-title-2 {
      color: #d3deec;
      font-size: 0.875rem;
      font-weight: 400;
    }
  }
}
</style>