<template>
  <div class="workplace">
    <div
      class="workplace-title"
      :class="projectId2 != null ? 'workplace-title-section' : ''"
    ></div>
    <div class="workplace-content">
      <div class="workplace-content-item">
        <div class="titleBg">
          <div class="titleIcon"></div>
          <div class="title">
            <div class="title1">单位总数：</div>
            <div class="title2">{{ data.supervisionSectionNum + data.constructionSectionNum + data.designSectionNum }}</div>
            <div class="title1">家</div>
          </div>
        </div>
        <div class="item-content">
          <div class="item-content-item">
            <div class="item-num">{{ data.supervisionSectionNum }}</div>
            <div class="item-icon item-icon-1"></div>
            <div class="item-title">监理单位</div>
          </div>
          <div class="item-content-item">
            <div class="item-num">{{ data.constructionSectionNum }}</div>
            <div class="item-icon item-icon-2"></div>
            <div class="item-title">施工单位</div>
          </div>
          <div class="item-content-item">
            <div class="item-num">{{ data.designSectionNum }}</div>
            <div class="item-icon item-icon-3"></div>
            <div class="item-title">设计单位</div>
          </div>
        </div>
      </div>
      <div class="workplace-content-item">
        <div class="titleBg">
          <div class="titleIcon titleIcon2"></div>
          <div class="title">
            <div class="title1">参建人员总数：</div>
            <div class="title2">{{ data.supervisionPersonNum + data.constructionPersonNum + data.designPersonNum }}</div>
            <div class="title1">人</div>
          </div>
        </div>
        <div class="item-content">
          <div class="item-content-item">
            <div class="item-num item-num-2">{{ data.supervisionPersonNum }}</div>
            <div class="item-icon item-icon-4"></div>
            <div class="item-title">监理管理人员</div>
          </div>
          <div class="item-content-item">
            <div class="item-num item-num-2">{{ data.constructionPersonNum }}</div>
            <div class="item-icon item-icon-5"></div>
            <div class="item-title">施工管理人员</div>
          </div>
          <div class="item-content-item">
            <div class="item-num item-num-2">{{ data.designPersonNum }}</div>
            <div class="item-icon item-icon-6"></div>
            <div class="item-title">设计管理人员</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
const store = useStore();
let projectId2 = ref(null);
let data = ref({
  constructionPersonNum: 0,
  constructionSectionNum: 0,
  designPersonNum: 0,
  designSectionNum: 0,
  supervisionPersonNum: 0,
  supervisionSectionNum: 0,
});

onMounted(() => {
  getWorkplaceData({ projectId: store.state.projectId, sectionId: store.state.sectionId });
});

watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    projectId2.value = projectId;
    if (sectionId) {
      getWorkplaceSectionData({ projectId, sectionId });
    } else {
    getWorkplaceData({ projectId, sectionId });
    }
  }
);

const getWorkplaceData = async (params) => {
  let res = await api.getWorkplaceData(params);
  data.value = res.result
};

const getWorkplaceSectionData = async (params) => {
  let res = await api.getWorkplaceSectionData(params);
  data.value = res.result
};

</script>

<style lang="less" scoped>
.workplace {
  width: 28.75rem;
  height: 25.25rem;

  .workplace-title {
    background: url("../../assets/img/workplace/title.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }
  .workplace-title-section {
    background: url("../../assets/img/workplace/title-section.png") center
      center no-repeat;
    background-size: 100% 100%;
  }

  .workplace-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 28.5rem;
    width: 100%;
    position: absolute;

    .workplace-content-item {
      background: url("../../assets/img/workplace/item-bg.png") center center
        no-repeat;
      background-size: 100% 100%;
      height: 12.75rem;
      width: 100%;
      margin: 0 0.9375rem;
      margin-top: 0.9375rem;

      .titleBg {
        width: 100%;
        height: 2.25rem;
        background: linear-gradient(
          to right,
          rgba(71, 152, 247, 0.2),
          rgba(71, 152, 247, 0)
        );
        display: flex;
        align-items: center;
        .titleIcon {
          background: url("../../assets/img/workplace/workplaceIcon.png") center
            center no-repeat;
          background-size: 100% 100%;
          height: 1.25rem;
          width: 1.25rem;
          margin-left: 0.9375rem;
        }
        .titleIcon2 {
          background: url("../../assets/img/workplace/userIcon.png") center
            center no-repeat;
          background-size: 100% 100%;
          height: 1.25rem;
          width: 1.25rem;
        }
        .title {
          display: flex;
          margin-left: 0.625rem;
          align-items: flex-end;
          .title1 {
            font-size: 0.875rem;
            font-weight: 400;
          }
          .title2 {
            font-size: 1.125rem;
            font-weight: 700;
          }
        }
      }

      .item-content {
        width: 100%;
        height: calc(100% - 2.25rem);
        display: flex;
        justify-content: center;
        align-items: center;
        justify-content: space-evenly;
        .item-content-item {
          width: 6.25rem;
          height: 8rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;

          .item-num {
            font-size: 1.25rem;
            font-weight: 700;
            color: #3cc4fe;
          }
          .item-num-2 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #3dffff;
          }
          .item-title {
            font-size: 0.875rem;
            font-weight: 400;
            color: #d3deec;
          }
          .item-icon {
            height: 4rem;
            width: 4rem;
          }

          .item-icon-1 {
            background: url("../../assets/img/workplace/icon1.png") center
              center no-repeat;
            background-size: 100% 100%;
          }
          .item-icon-2 {
            background: url("../../assets/img/workplace/icon2.png") center
              center no-repeat;
            background-size: 100% 100%;
          }
          .item-icon-3 {
            background: url("../../assets/img/workplace/icon3.png") center
              center no-repeat;
            background-size: 100% 100%;
          }
          .item-icon-4 {
            background: url("../../assets/img/workplace/icon4.png") center
              center no-repeat;
            background-size: 100% 100%;
          }
          .item-icon-5 {
            background: url("../../assets/img/workplace/icon5.png") center
              center no-repeat;
            background-size: 100% 100%;
          }
          .item-icon-6 {
            background: url("../../assets/img/workplace/icon6.png") center
              center no-repeat;
            background-size: 100% 100%;
          }
        }
      }
    }
  }
}
</style>
