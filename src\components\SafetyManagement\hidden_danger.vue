<template>
  <div class="projectOverview">
    <div class="projectOverview-title"></div>
    <div class="projectOverview-content">
      <div class="statistics">
        <div class="left-box">
          <div class="top">
            <!-- <img src="../../assets/img/SafetyManagement/隐患总数.png" alt="" /> -->

            <div id="santj" style="width: 7.5rem; height: 7.5rem"></div>

            <div class="textimg">
              <!-- <img
                id="yinhimg"
                src="../../assets/img/SafetyManagement/隐患统计1.png"
                alt=""
              /> -->
              <!-- <div class="text"> -->
              <p class="a">{{ yinhzos }}</p>
              <p class="b">隐患总数</p>
              <!-- </div> -->
            </div>
          </div>

          <div class="bottom">
            <div class="son-top">
              <div class="yibyan">
                <div class="a-box"></div>
                <p>一般隐患</p>
              </div>
              <div class="bianf">
                <p>{{ yibyinh }}</p>
                <p>占比：{{ percentageYibyinh }}%</p>
              </div>
            </div>
            <div class="son-bottom">
              <div class="yibyan">
                <div class="a-box"></div>
                <p>重大隐患</p>
              </div>
              <div class="bianf">
                <p>{{ zdayinh }}</p>
                <p>占比：{{ percentagezdayinh }}%</p>
              </div>
            </div>
          </div>
        </div>
        <img src="../../assets/img/SafetyManagement/隔开线.png" alt="" />
        <div class="right-box">
          <!-- <div id="main" style="width: 600px; height: 400px"></div> -->
          <div class="son-topa">
            <div class="left">
              <img
                src="../../assets/img/SafetyManagement/整改趋势.png"
                alt=""
              />
              <p>整改趋势</p>
            </div>
            <div class="right">
              <p
                :class="{ 'text-color': selectedIndex === 1 }"
                @click="selectItem(1)"
              >
                近一周
              </p>
              <p
                :class="{ 'text-color': selectedIndex === 2 }"
                @click="selectItem(2)"
              >
                近一月
              </p>
              <p
                :class="{ 'text-color': selectedIndex === 3 }"
                @click="selectItem(3)"
              >
                近一年
              </p>
            </div>
          </div>
          <div class="son-bottoma">
            <div class="left-a">
              <div
                id="shexiang"
                style="width: 34.8125rem; height: 9.8125rem"
              ></div>
              <!-- <img
                src="../../assets/img/SafetyManagement/日期折线图.png"
                alt=""
              /> -->
            </div>
            <img
              style="height: 9.25rem"
              src="../../assets/img/SafetyManagement/隔开线.png"
              alt=""
            />
            <div class="right-a">
              <div class="text">
                <p class="text1">{{ zhengwclv }}<span class="text2">%</span></p>
                <p class="text3">整改完成率</p>
              </div>
              <!-- <div class="text-box">
                <p class="liu">67.50<span class="bf">%</span></p>
                <p class="jishi">及时整改率</p>
              </div> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
        
        
<script setup>
import { onMounted, ref, watch, onBeforeUnmount } from "vue";
import * as echarts from "echarts";
import api from "@/api/index";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
const route = useRoute();
let store = useStore();
const main = ref(null);
onMounted(() => {
  // if (!store.state.projectId && !store.state.sectionId) {
    hiddenTroublex();
    rectifyTrendx();
  // }
  // hiddenTroublex();
  console.log("当前路由路径：", route.path);
});
// 监听
watch(
  () => [store.state.projectId, store.state.sectionId],
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      hiddenTroublex();
      rectifyTrendx();
    }
  }
);
// 隐患
let yinhzos = ref(0); //隐患总数
let yibyinh = ref(0); //一般隐患
let zdayinh = ref(0); //重大隐患
let percentageYibyinh = ref(0); //一般隐患所占的百分比
let percentagezdayinh = ref(0); //重大隐患所占的百分比
let zhengwclv = ref(0);
const hiddenTroublex = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  api
    .hiddenTrouble(params)
    .then((res) => {
      let data = res.data;
      console.log("隐患总数", data);
      if (route.path == "/safetyManagement") {
        shanxing(data);
      }
      yinhzos.value = data.totalTroubleCount;
      yibyinh.value = data.normalTroubleCount;
      zdayinh.value = data.seriousTroubleCount;

      // 计算一般隐患所占的百分比
      percentageYibyinh.value = yinhzos.value == 0 ? 0 : ((yibyinh.value / yinhzos.value) * 100).toFixed(
        1
      );
      // 计算重大隐患所占的百分比
      percentagezdayinh.value = yinhzos.value == 0 ? 0 : ((zdayinh.value / yinhzos.value) * 100).toFixed(
        1
      );
    })
    .catch((err) => {
      console.log(err, "隐患总数失败");
    });
};
// 整改趋势
let zhexiandata = ref([]);
const rectifyTrendx = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let type = selectedIndex.value;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
    type: type,
  };
  api
    .rectifyTrend(params)
    .then((res) => {
      let data = res.data.data;
      // 周
      if (Array.isArray(data) && data.length == 7) {
        let today = new Date();
        let formattedDates = [];
        for (let i = 6; i >= 0; i--) {
          // 从今天往前数7天
          let date = new Date(today);
          date.setDate(today.getDate() - i);
          let formattedDate = `${date.getMonth() + 1}-${date.getDate()}`;
          formattedDates.push(formattedDate);
        }
        zhexiandata.value = formattedDates;
        if (route.path == "/safetyManagement") {
          zhexian(data, zhexiandata.value);
        }
        let axx = data.map((item) => item.rectifyComplete);
        let sum = axx.reduce(
          (accumulator, currentValue) => accumulator + currentValue,
          0
        );
        let result;
        if (res.data.rectifyCompleteRate === 0) {
          result = 0; 
        } else {
          // 计算除法并保留一位小数
          result = (sum / res.data.rectifyCompleteRate).toFixed(1);
        }
        zhengwclv.value = result;
        // 月
      } else if (Array.isArray(data) && data.length == 30) {
        let today = new Date();
        let formattedDates = [];
        for (let i = 30; i >= 0; i--) {
          // 从今天往前数30天
          let date = new Date(today);
          date.setDate(today.getDate() - i);
          let formattedDate = `${date.getMonth() + 1}-${date.getDate()}`;
          formattedDates.push(formattedDate);
        }
        zhexiandata.value = formattedDates;
        if (route.path == "/safetyManagement") {
          zhexian(data, zhexiandata.value);
        }
        let axx = data.map((item) => item.rectifyComplete);
        let sum = axx.reduce(
          (accumulator, currentValue) => accumulator + currentValue,
          0
        );
        let result;
        if (res.data.rectifyCompleteRate === 0) {
          result = 0; 
        } else {
          // 计算除法并保留一位小数
          result = (sum / res.data.rectifyCompleteRate).toFixed(1);
        }
        zhengwclv.value = result;
        // 年
      } else if (Array.isArray(data) && data.length == 12) {
        let today = new Date();
        let currentMonth = today.getMonth() + 1;
        let formattedMonths = [];

        for (let i = 0; i < 12; i++) {
          let calculatedMonth = currentMonth - i;
          if (calculatedMonth <= 0) {
            calculatedMonth += 12; // 处理月份为负数时，加上12转换为有效月份
          }
          formattedMonths.push(calculatedMonth + "月");
        }

        formattedMonths.reverse(); // 将数组倒序，使得最近的月份在前面

        zhexiandata.value = formattedMonths;
        console.log(zhengwclv.value, "sadkbnasd");
        if (route.path == "/safetyManagement") {
          zhexian(data, zhexiandata.value);
        }
        let axx = data.map((item) => item.rectifyComplete);
        let sum = axx.reduce(
          (accumulator, currentValue) => accumulator + currentValue,
          0
        );
        let result;
        if (res.data.rectifyCompleteRate === 0) {
          result = 0; 
        } else {
          // 计算除法并保留一位小数
          result = (sum / res.data.rectifyCompleteRate).toFixed(1);
        }
        zhengwclv.value = result;
      }
    })
    .catch((err) => {
      console.log(err, "整改趋势请求失败");
    });
};

// 扇形统计图
const shanxing = async (data) => {
  let santjElement = document.getElementById("santj");
  // 销毁已有的 echarts 实例

  echarts.dispose(santjElement);

  const santj = await echarts.init(document.getElementById("santj"));
  const canto = ref({
    tooltip: {
      trigger: "item",
    },
    legend: {
      top: "5%",
      left: "center",
      show: false,
    },
    series: [
      {
        type: "pie",
        center: ["50%", "50%"],
        radius: ["60%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          normal: {
            color: "blue",
            opacity: 1,
            transform: `scale: (1.2)`,
          },
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            name: "一般隐患",
            value: data.normalTroubleCount ? data.normalTroubleCount : 0,
            itemStyle: { color: "#f7c034" },
          },
          {
            name: "重大隐患",
            value: data.seriousTroubleCount ? data.seriousTroubleCount : 0,
            itemStyle: { color: "#ff5046" },
          },
        ],
      },
    ],
  });
  santj.setOption(canto.value);
};
// 折线统计图
const zhexian = async (data, datains) => {
  let shexiangElement = document.getElementById("shexiang");
  // 销毁已有的 echarts 实例
  if (shexiangElement) {
    echarts.dispose(shexiangElement);
  }

  const zhex = await echarts.init(document.getElementById("shexiang"));
  let arr1 = data.map((item) => item.rectifyTotal);
  let arr2 = data.map((item) => item.rectifyComplete);
  let arr3 = data.map((item) => item.rectifyUnComplete);
  const seriesData = [
    {
      name: "总整改",
      type: "line",
      data: arr1, // 假设这里是总整改的数据，如果有对应的属性名，请替换
      itemStyle: {
        color: "#4798F7",
      },
    },
    {
      name: "已整改",
      type: "line",
      data: arr2, // 这里替换为实际的属性名
      itemStyle: {
        color: "#3DFFFF",
      },
    },
    {
      name: "未整改",
      type: "line",
      data: arr3, // 这里替换为实际的属性名
      itemStyle: {
        color: "#F7C034",
      },
    },
  ];
  // 指定图表的配置项和数据
  const option = ref({
    title: {
      text: "整改数",

      textStyle: {
        fontSize: 12, // 设置字体大小
        color: "#D3DEEC", // 设置字体颜色
        fontWeight: 400,
      },
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["总整改", "已整改", "未整改"],
      textStyle: {
        color: "#4798F7", // 设置Email系列的颜色
        fontSize: 12,
        fontWeight: 400,
        color: "#fff",
      },
    },
    grid: {
      top: "22%",
      left: "0%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
    // toolbox: {
    //   feature: {
    //     saveAsImage: {},
    //   },
    // },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: datains,
      axisLabel: {
        textStyle: {
          fontSize: 12, // 设置字体大小
          color: "#D3DEEC", // 设置字体颜色
          fontWeight: 400,
        },
      },
    },
    yAxis: {
      type: "value",
      min: 0, // 设置 y 轴的最小值为 0
      // max: 100, // 设置 y 轴的最大值为 100
      // axisLabel: {
      //   formatter: "{value} ", // 设置 y 轴数值的格式
      // },
      axisLabel: {
        textStyle: {
          fontSize: 12, // 设置字体大小
          color: "#D3DEEC", // 设置字体颜色
          fontWeight: 500,
        },
      },
    },

    series: seriesData,
  });

  // 使用刚指定的配置项和数据显示图表。
  zhex.setOption(option.value);
};

// 选择周月年
const selectedIndex = ref(1);
const selectItem = (index) => {
  selectedIndex.value = index;
  rectifyTrendx();
};
</script>
        
 <style lang="less" scoped>
.text-color {
  color: #3dffff;
  font-size: 0.875rem;
  font-weight: 400;
  border-bottom: 1px solid rgb(1, 233, 202);
}
#santj {
  // position: relative; /* 增加定位，方便控制子元素 */
  z-index: 99;
}
#santj > * {
  /* 具体的图形元素 */
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1.2); /* 缩放因子，这里放大120% */
}
#yinhimg {
  width: 6.25rem;
  z-index: -1;
}
.projectOverview {
  width: 58.5rem;
  //   height: 38.75rem;

  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/隐患级别统计.png") center
      center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 28.75rem;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 13.0625rem;
    width: 58.5rem;
    margin-top: 0.3125rem;
    //   position: absolute;
    padding: 0.75rem 1rem;
    box-sizing: border-box;
    > .statistics {
      width: 100%;
      height: 100%;
      display: flex;
      > .left-box {
        width: 14.375rem;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        // border-right: 1px #d3deec dashed;
        // margin-right: 2.25rem;
        > .top {
          margin-bottom: 0.75rem;
          position: relative;
          background: url("../../assets/img/SafetyManagement/隐患2.png") center
            center no-repeat;
          background-size: 100% 100%;
          > .textimg {
            width: 5rem;
            height: 5rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: url("../../assets/img/SafetyManagement/隐患.png") center
              center no-repeat;
            background-size: 100% 100%;

            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            > .a {
              font-weight: 700;
              font-size: 1.5rem;
            }
            > .b {
              font-size: 0.75rem;
              font-weight: 400;
            }
          }

          > img {
            width: 7.5rem;
          }
        }
        > .bottom {
          width: 10.75rem;
          height: 3rem;
          > .son-top {
            width: 110%;
            height: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            > .yibyan {
              display: flex;
              align-items: center;
              > .a-box {
                width: 0.5rem;
                height: 0.5rem;
                background-color: #f7c034;
                margin-right: 0.25rem;
              }
              > p {
                font-weight: 400;
                font-size: 0.75rem;
                color: #d3deec;
              }
            }
            > .bianf {
              display: flex;
              align-items: center;
              min-width: 7.1rem;
              > p {
                font-size: 0.875rem;
                font-weight: 500;
                &:nth-child(2) {
                  margin-left: 1rem;
                }
              }
            }
          }
          .son-bottom {
            width: 110%;
            height: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            > .yibyan {
              display: flex;
              align-items: center;
              > .a-box {
                width: 0.5rem;
                height: 0.5rem;
                background-color: #ff5046;
                margin-right: 0.25rem;
              }
              > p {
                font-weight: 400;
                font-size: 0.75rem;
                color: #d3deec;
              }
            }
            > .bianf {
              display: flex;
              align-items: center;
              min-width: 7.1rem;
              > p {
                font-size: 0.875rem;
                font-weight: 500;
                &:nth-child(2) {
                  margin-left: 1rem;
                }
              }
            }
          }
        }
      }

      > .right-box {
        width: 41.4375rem;
        height: 100%;
        margin-left: 2.1875rem;
        > .son-topa {
          display: flex;
          align-items: center;
          justify-content: space-between;
          > .left {
            display: flex;
            align-items: center;
            > img {
              width: 1rem;
              height: 1rem;
            }
            > p {
              font-size: 0.875rem;
              font-weight: 400;
            }
          }
          > .right {
            display: flex;
            align-items: center;
            > p {
              cursor: pointer;
              font-weight: 400;
              font-size: 0.75rem;

              // padding-bottom: 1px; /* 控制下划线与文字之间的距离 */
              &:nth-child(2) {
                margin: 0 1rem;
              }
            }
          }
        }

        > .son-bottoma {
          height: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          > .left-a {
            width: 34.5625rem;
            height: 9.8125rem;
            margin-top: 0.625rem;
            > img {
              width: 34.5625rem;
              height: 9.8125rem;
            }
          }
          > .right-a {
            height: 100%;
            margin-left: 1rem;
            width: 4rem;
            // height: 100%;
            display: flex;
            align-items: center;
            > .text {
              width: 4rem;
              height: 50%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              > .text1 {
                font-weight: 700;
                font-size: 1.25rem;
                color: #3dffff;
                > .text2 {
                  color: #fff;
                  font-weight: 400;
                  font-size: 0.75rem;
                }
              }
              > .text3 {
                font-weight: 400;
                font-size: 0.75rem;
                color: #d3deec;
              }
            }

            > .text-box {
              height: 50%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              > .liu {
                font-weight: 700;
                font-size: 1.25rem;
                > .bf {
                  font-weight: 400;
                  font-size: 0.75rem;
                }
              }

              > .jishi {
                font-weight: 400;
                font-size: 0.75rem;
                color: #d3deec;
              }
            }
          }
        }
      }
    }
  }
}
</style>