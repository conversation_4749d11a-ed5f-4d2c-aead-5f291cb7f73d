<template>
  <div class="projectOverview">
    <div class="projectOverview-title"></div>
    <div class="projectOverview-content">
      <div class="change-0" v-if="changex == 0">
        <div class="interlayer"></div>
        <div class="top">
          <div class="plan">
            <img
              src="../../assets/img/SafetyManagement/左侧图标@2x.png"
              alt=""
            />
            <div class="cost">
              <ul class="list">
                <li class="item">
                  <p class="text1">{{ jihuaPrice.toFixed(2) }}</p>
                  <p class="text2">计划费用 (万元)</p>
                </li>
                <li class="item">
                  <p class="text1">{{zhifuPrice}}</p>
                  <p class="text2">支付费用 (万元)</p>
                </li>
                <li class="item">
                  <p class="text1">{{ jihuaPrice == 0 ? 0 : (zhifuPrice / jihuaPrice * 100 ).toFixed(2) }}%</p>
                  <p class="text2">支付率</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="form">
            <!-- <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="date" label="Date" width="180" />
              <el-table-column prop="name" label="Name" width="180" />
              <el-table-column prop="address" label="Address" />
            </el-table> -->
            <div class="table__header">
              <div class="text1">{{ store.getters.isProjectScreen == true ? '标段' : '项目' }}名称</div>
              <div class="text2">计划费用 (万元)</div>
              <div class="text3">支付费用 (万元)</div>
              <div class="text4">支付率</div>
            </div>
            <div class="table-tr">
              <div class="item" v-for="(item, index) in tableData" :key="index">
                <div class="te1">{{ item.name }}</div>
                <div class="te2">
                  {{ item.planAmount }}
                </div>
                <div class="te3">
                  {{ item.costAmount }}
                </div>
                <div class="te4">
                  {{ item.payment }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="change-1" v-if="changex == 1">
        <div class="top">
          <img
            src="../../assets/img/SafetyManagement/安全生产投入-支付率2.png"
            alt=""
          />
          <p class="text">
            支付率:<span class="text1"><span class="text2">{{ jihuaPrice == 0 ? 0 : (zhifuPrice / jihuaPrice).toFixed(2) }}</span>%</span>
          </p>
        </div>
        <div class="bottom">
          <div id="zhuzx" style="width: 100%; height: 100%"></div>
        </div>
      </div>
    </div>
  </div>
</template>
    
    
    <script setup>
import { onMounted, ref, watch } from "vue";
import * as echarts from "echarts";
import api from "@/api/index";
import { useStore } from "vuex";
let store = useStore();
let changex = ref(store.getters.isSectionScreen == true ? 1 : 0);
const jihuaPrice = ref(0)
const zhifuPrice = ref(0)

onMounted(() => {
  // if (changex.value == 1) {
  //   zhuztx();
  // }
  // if (!store.state.projectId && !store.state.sectionId) {
    // anqcost();
  // }
  if (changex.value == 1) {
    pianxdefra();
  }else{
    anqcost();
  }
});

// 渲染数组
const tableData = ref([]);
// 监听
watch(
  () => [store.state.projectId, store.state.sectionId],
  (newVal, oldVal) => {
    jihuaPrice.value = 0
      zhifuPrice.value = 0
    if (newVal !== oldVal) {
      changex.value = 0;
      anqcost();
    }
    if (store.state.sectionId) {
      changex.value = 1;
      pianxdefra();
    }
  }
);
// 处理两个数组的合并
const anqcost = () => {
  Promise.all([anqplanx(), costplanx()])
    .then(([anqx, cost]) => {
      for (let value1 of anqx) {
        for (let value2 of cost) {
          if (value1.id === value2.id) {
            const paymentRate =
              value1.planAmount !== 0 && value2.costAmount !== 0
                ? ((value2.costAmount / value1.planAmount) * 100).toFixed(2)
                : 0;
            tableData.value.push({
              ...value1,
              ...value2,
              payment: paymentRate + "%",
            });
            jihuaPrice.value += value1.planAmount
            zhifuPrice.value += value2.costAmount
          }
        }
      }
      console.log(tableData.value, "tableData");
    })
    .catch((err) => {
      // 处理任一请求失败的情况
      console.log(err, "安全生产投入请求失败");
    });
};
// const mergeArraysById = (arr1, arr2) => {
//   // 将 arr1 和 arr2 合并成一个数组
//   const mergedArray = [...arr1, ...arr2];

//   // 根据 id 进行分组
//   const groupedById = mergedArray.reduce((acc, obj) => {
//     const key = obj.id;
//     if (!acc[key]) {
//       acc[key] = [];
//     }
//     acc[key].push(obj);
//     return acc;
//   }, {});

//   // 将每个 id 对应的数组对象合并成一个新对象，并组成结果数组
//   const result = Object.values(groupedById).map((group) => ({
//     ...group.reduce((acc, obj) => ({ ...acc, ...obj }), {}),
//   }));
//   console.log(result, "总数据数组");
//   return result;
// };

// 安全生产投入-计划费用(中心-项目)
const anqplanx = async () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  let data = await api.anqplan(params);
  return data.data;
};
// 安全生产投入-支付费用(中心-项目)
const costplanx = async () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  let data1 = await api.costplan(params);
  console.log(data1, "安全生产投入-支付费用(中心-项目)");
  return data1;
};
// 同时处理两个数据(标段)
const pianxdefra = () => {
  Promise.all([sectionpianx(), defraymentx()])
    .then(([anqx, cost]) => {
      console.log(anqx, cost, "计划费用(标段)");
      zhuztx(anqx, cost);
      jihuaPrice.value = anqx
      zhifuPrice.value = cost
    })
    .catch((err) => {
      // 处理任一请求失败的情况
      console.log(err, "计划费用(标段)请求失败");
    });
};
// 安全生产投入-计划费用(标段)
const sectionpianx = async () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  let data = await api.sectionpian(params);

  return data.data;
};
// 安全生产投入-支付费用(标段)
const defraymentx = async () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  let data = await api.defrayment(params);
  return data;
};
// 柱状统计图
const zhuztx = async (anqx, cost) => {
  let zhuzElement = document.getElementById("zhuzx");
  // 销毁已有的 echarts 实例
  echarts.dispose(zhuzElement);

  let zhuz = await echarts.init(document.getElementById("zhuzx"));
  let zhuzoptionx = ref({
    width: '70%',
    xAxis: {
      name: "金额：万元",
      // max: 100,
      type: "value",
      nameTextStyle: {
        fontSize: 12,
        color: "rgba(255, 255, 255, 0.6)",
      },
      axisLabel: {
        textStyle: {
          fontSize: 12, // 设置字体大小
          color: "#D3DEEC", // 设置字体颜色
          fontWeight: 400,
        },
      },
    },
    yAxis: {
      name: "费用",
      type: "category",
      nameLocation: "start",
      nameTextStyle: {
        fontSize: 12,
        color: "rgba(255, 255, 255, 0.6)",
      },
      data: [
        {
          value: "计划费用",
          textStyle: { fontSize: 12, color: "#D3DEEC", fontWeight: 400 },
        },
        {
          value: "支付费用",
          textStyle: { fontSize: 12, color: "#D3DEEC", fontWeight: 400 },
        },
      ],
      inverse: true,
    },
    grid: {
      top: "22%",
      left: "3%",
      right: "11%",
      bottom: "5%",
      containLabel: true,
    },
    series: [
      {
        data: [
          {
            value: anqx ? anqx : 0,
            // type: "bar",
            name: "计划费用",
            label: {
              show: true,
              position: "right", // 可根据需要调整标签位置
              textStyle: {
                fontSize: 12, // 设置标签字体大小
                color: "#fff", // 设置标签字体颜色
                fontWeight: 500,
              },
            },
            itemStyle: {
              showBackground: true,
              barBorderRadius: [0, 8, 8, 0], // 设置圆角，数组内四个值分别为左上、右上、右下、左下的圆角半径
              color: {
                type: "linear",
                x: 1, // 横向渐变从左到右
                y: 0, // 垂直方向上渐变没有变化，保持0
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: "#FFFFFF", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: "#4798F7", // 自定义第二个柱状图的颜色
                  },
                  {
                    offset: 0.9,
                    color: "#023164", // 自定义第二个柱状图的颜色
                  },
                ],
              },
            },
          },
          {
            value: cost ? cost : 0,
            // type: "bar",
            name: "支付费用",
            label: {
              show: true,
              position: "right", // 可根据需要调整标签位置
              textStyle: {
                fontSize: 12, // 设置标签字体大小
                color: "#fff", // 设置标签字体颜色
                fontWeight: 500,
              },
            },
            itemStyle: {
              showBackground: true,
              barBorderRadius: [0, 8, 8, 0], // 设置圆角，数组内四个值分别为左上、右上、右下、左下的圆角半径
              color: {
                type: "linear",
                x: 1, // 横向渐变从左到右
                y: 0, // 垂直方向上渐变没有变化，保持0
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: "#FFFFFF", // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: "#3DFFFF", // 50% 处的颜色
                  },
                  {
                    offset: 0.9,
                    color: "#023164", // 90% 处的颜色
                  },
                ],
              },
            },
          },
        ],
        type: "bar",
        barWidth: 12,
        showBackground: true,
        backgroundStyle: {
          color: "rgba(180, 180, 180, 0.2)",
          borderRadius: [0, 12, 12, 0], // 柱状图的圆角设置
        },
      },
    ],
  });
  zhuz.setOption(zhuzoptionx.value);
};
</script>
    
  <style lang="less" scoped>
.projectOverview {
  width: 28.75rem;
  //   height: 38.75rem;

  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/安全生产投入面板标题@2x.png")
      center center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 17.9375rem;
    width: 100%;
    margin-top: 0.3125rem;
    //   position: absolute;
    padding: 0.75rem 1rem;
    box-sizing: border-box;
    //   .interlayer {
    //     height: 1.25rem;
    //   }
    .change-0 {
      > .top {
        width: 100%;
        height: 3.75rem;
        padding: 0.625rem 1rem;
        box-sizing: border-box;
        > .plan {
          width: 100%;
          display: flex;
          justify-content: space-between;
          > img {
            width: 3rem;
            height: 2.5rem;
          }
          > .cost {
            width: 20.25rem;
            height: 2.625rem;
            //   background-color: #ff5046;
            display: flex;
            justify-content: space-between;
            > .list {
              width: 100%;
              display: flex;
              justify-content: space-between;
              > .item {
                > .text1 {
                  font-weight: 700;
                  font-size: 1.25rem;
                }
                > .text2 {
                  font-weight: 400;
                  font-size: 0.75rem;
                  color: #e2f2fb;
                }
              }
            }
          }
        }
      }

      > .bottom {
        margin-top: 0.625rem;
        width: 100%;
        height: 10rem;
        //   padding: 0 1.25rem;

        box-sizing: border-box;
        .form {
          width: 100%;
          // /deep/.el-table{

          //     --el-table-tr-bg-color: rgba(196, 75, 75, 0) !important;
          //     --el-table-bg-color:rgba(223, 0, 0, 0) !important;
          //     --el-table-header-bg-color: rgba(44, 164, 255, 0.322) !important;
          // }
          // /deep/.el-table__header-wrapper{
          //     height: 2rem !important;
          // }

          // /deep/.el-table__header{
          //     width: 100% !important;
          //     height: 2rem;
          // }
          .table__header {
            width: 100%;
            height: 2rem;
            background-color: rgba(44, 164, 255, 0.322);
            display: flex;
            .text1 {
              width: 8.25rem;
              height: 2rem;
              text-align: left;
              color: #2ca3ff;
              padding-left: 0.625rem;
              box-sizing: border-box;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }
            .text2 {
              width: 7.25rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text3 {
              width: 7.25rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }
            .text4 {
              width: 4rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }
          }

          .table-tr {
            width: 100%;
            height: 8rem;
            overflow: hidden;
            overflow-y: auto;
            .item {
              width: 100%;
              border-bottom: 0.0625rem solid rgba(44, 164, 255, 0.322);
              display: flex;
              > .te1 {
                width: 8.125rem;
                height: 2rem;
                text-align: left;
                color: #ffffff;
                padding-left: 0.625rem;
                box-sizing: border-box;
                line-height: 2rem;
                font-size: 0.875rem;
                overflow: hidden;
              }
              > .te2 {
                width: 7.25rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }
              > .te3 {
                width: 7.25rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }
              > .te4 {
                width: 4rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }
            }
          }
        }
      }
    }

    .change-1 {
      width: 100%;
      height: 2.75rem;
      background: url("../../assets/img/SafetyManagement/安全生产投入-支付率3.png")
        center center no-repeat;
      background-size: 100% 100%;
      > .top {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        > img {
          width: 3.375rem;
          height: 2.75rem;
        }
        > .text {
          font-weight: 400;
          font-size: 0.875rem;
          color: #d3deec;
          margin-left: 1rem;
          > .text1 {
            // font-weight: 700;
            font-size: 0.75rem;
            color: #ffffff;
            > .text2 {
              font-weight: 700;
              font-size: 1.25rem;
              color: #ffffff;
            }
          }
        }
      }

      .bottom {
        width: 100%;
        height: 9rem;
        margin-top: 1.5rem;
      }
    }
  }
}

.color-a {
  color: #3dffff;
}
.color-b {
  color: #ff5046;
}
</style>