<template>
  <div class="zhijianpingdingBox">
    <div class="title-box"></div>
    <div class="neirong-box">
      <div class="neirong-top">
        <img
          src="../../assets/img/zhijianpingdingBox/左侧图标 (1).png"
          alt=""
        />
        <div class="text-box">
          <p class="text-1">
            {{ (inspectionData.danweiCompleteRate * 100).toFixed(2) }}<span>%</span>
          </p>
          <p class="text-2">单位工程完成率</p>
        </div>
        <div class="text-box">
          <p class="text-1">
            {{ (inspectionData.fenbuCompleteRate * 100).toFixed(2) }}<span>%</span>
          </p>
          <p class="text-2">分部工程完成率</p>
        </div>
        <div class="text-box">
          <p class="text-1">
            {{ (inspectionData.danyuanCompleteRate * 100).toFixed(2) }}<span>%</span>
          </p>
          <p class="text-2">单元工程完成率</p>
        </div>
      </div>
      <div class="tishi-box">
        <div class="tishi-item">
          <div class="sekuai sekuai1"></div>
          <p class="tishi-text">未评定</p>
        </div>
        <div class="tishi-item">
          <div class="sekuai sekuai2"></div>
          <p class="tishi-text">评定中</p>
        </div>
        <div class="tishi-item">
          <div class="sekuai sekuai3"></div>
          <p class="tishi-text">已评定</p>
        </div>
        <div class="tishi-item">
          <div class="sekuai sekuai4"></div>
          <p class="tishi-text">评定率</p>
        </div>
      </div>
      <li class="biaotou">
        <p class="text-name">{{ store.getters.isProjectScreen == true ? '标段' : '项目' }}名称</p>
        <p class="text-danwei">单位工程</p>
        <p class="text-fengong">分部工程</p>
        <p class="text-danyuan">单元工程</p>
      </li>
      <ul class="biao">
        <li
          class="biaotou liebiao"
          v-for="item in inspectionData.data"
          :key="'gongchengliebiao' + item"
        >
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="item.name"
            placement="top"
          >
            <el-text class="text-name" truncated>{{ item.name }}</el-text>
          </el-tooltip>
          <p class="text-danwei">
            <span class="span1">{{ item.danwei.toEvaluation }}</span
            >/ <span class="span2">{{ item.danwei.inEvaluation }}</span
            >/ <span class="span3">{{ item.danwei.evaluated }}</span>
            {{ item.danwei.evaluationRate }}%
          </p>
          <p class="text-fengong">
            <span class="span1">{{ item.fenbu.toEvaluation }}</span
            >/ <span class="span2">{{ item.fenbu.inEvaluation }}</span
            >/ <span class="span3">{{ item.fenbu.evaluated }}</span>
            {{ item.fenbu.evaluationRate }}%
          </p>
          <el-text class="el-text" truncated>
            <p class="text-danyuan">
            <span class="span1">{{ item.danyuan.toEvaluation }}</span
            >/ <span class="span2">{{ item.danyuan.inEvaluation }}</span
            >/ <span class="span3">{{ item.danyuan.evaluated }}</span>
            {{ (item.danyuan.evaluationRate * 100).toFixed(2) }}%
          </p>
          </el-text>
        </li>
      </ul>
    </div>
  </div>
</template>
  
  <script>
import { reactive, toRefs, onBeforeMount, onMounted, watch } from "vue";
import api from "@/api/index.js";
import { useStore } from "vuex";
export default {
  name: "",
  setup() {
    const store = useStore();
    console.log("1-开始创建组件-setup");
    const data = reactive({
      inspectionData: {
        danweiCompleteRate: 0,
        fenbuCompleteRate: 0,
        danyuanCompleteRate: 0,
        data: [],
      },
    });
    onBeforeMount(() => {});
    onMounted(() => {
      inspection({
        projectId: store.state.projectId,
        sectionId: store.state.sectionId,
      });
    });
    watch(
      () => [store.state.projectId, store.state.sectionId],
      ([projectId, sectionId]) => {
        inspection({ projectId, sectionId });
      }
    );
    const inspection = async (params) => {
      let res = await api.inspection(params);
      console.log("质检评定：", res);
      data.inspectionData = res;
    };

    return {
      ...toRefs(data), store,
    };
  },
};
</script>
<style scoped lang='less'>
.zhijianpingdingBox {
  margin-top: 1rem;
  width: 100%;
  height: 100%;
  font-size: 0.700rem;
  .title-box {
    background: url("../../assets/img/zhijianpingdingBox/面板标题.png") center
      center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }
  .neirong-box {
    margin-top: 0.3125rem;
    width: 100%;
    height: 35.3125rem;
    padding: 1rem;
    box-sizing: border-box;
    .neirong-top {
      width: 100%;
      height: 3.75rem;
      padding: 0.625rem 1rem;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      font-size: 0.75rem;
      background: linear-gradient(
        273.05deg,
        rgba(0, 118, 169, 0.3) -0.16%,
        rgba(0, 118, 169, 0) 100%
      );
      border: 0.0625rem solid;
      border-image: linear-gradient(
          115.95deg,
          rgba(0, 151, 181, 0.8) 0%,
          rgba(0, 151, 181, 0.2) 22.03%,
          rgba(0, 151, 181, 0.2) 79.74%,
          rgba(0, 151, 181, 0.8) 100.72%
        )
        1;
      img {
        height: 100%;
      }
      .text-box {
        width: 5.25rem;
        text-align: center;
        .text-1 {
          font-size: 1.125rem;
          font-weight: 700;
          span {
            font-size: 0.75rem;
            font-weight: 100;
          }
        }
      }
    }
    .tishi-box {
      height: 1.125rem;
      margin: 1.25rem 0 0.75rem;
      display: flex;
      font-size: 0.75rem;
      justify-content: center;
      .tishi-item {
        height: 1.125rem;
        width: 3rem;
        display: flex;
        .sekuai {
          width: 0.5rem;
          height: 0.5rem;
          margin: 0.3125rem 0.125rem;
        }
        .sekuai1 {
          background: #ff5046;
        }
        .sekuai2 {
          background: #f7c034;
        }
        .sekuai3 {
          background: #3dffff;
        }
        .sekuai4 {
          background: #d3deec;
        }
        .tishi-text {
          line-height: 1.125rem;
        }
      }
    }

    .biaotou {
      height: 2rem;
      width: 100%;
      background: #2ca3ff33;
      display: flex;
      p {
        line-height: 2rem;
        font-weight: 700;
        color: #2ca3ff;
      }
      .text-name {
        width: 6rem;
        text-align: left;
        padding: 0 0 0 0.5rem;
        font-size: 0.700rem;
        color: #fff;
      }
      .text-danwei,
      .text-fengong,
      .text-danyuan {
        width: 6.75rem;
        text-align: center;
      }
      .el-text{
        font-size: 0.700rem;
      }
    }

    .biao {
      width: 100%;
      height: 26rem;
      overflow: auto;

      .liebiao {
        border-bottom: 0.0625rem solid #4798f766;
        background-color: #2ca3ff00;

        p {
          font-weight: 100;
          color: #d3deec;
          .span1 {
            color: #ff5046;
          }
          .span2 {
            color: #f7c034;
          }
          .span3 {
            color: #3dffff;
          }
        }
      }
    }
  }
}
</style>