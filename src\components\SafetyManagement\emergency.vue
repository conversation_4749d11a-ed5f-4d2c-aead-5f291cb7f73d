<template>
  <div class="projectOverview">
    <div class="projectOverview-title"></div>
    <div class="projectOverview-content">
      <div class="changx-0" v-if="changx == 0">
        <div class="top">
          <div class="left">
            <img src="../../assets/img/SafetyManagement/应急预案.png" alt="" />
            <div class="liability_statement">
              <p class="text1">{{ emergencyPlanCompletionRate }}%</p>
              <p class="text2">应急预案完成率</p>
            </div>
          </div>
          <div class="right">
            <img src="../../assets/img/SafetyManagement/应急演练.png" alt="" />
            <div class="management_system">
              <p class="text1">{{ emergencyDrillTotal }}</p>
              <p class="text2">应急演练开展(次)</p>
            </div>
          </div>
        </div>
        <div class="bottom">
          <div class="form">
            <!-- <el-table :data="tableData" style="width: 100%">
              <el-table-column prop="date" label="Date" width="180" />
              <el-table-column prop="name" label="Name" width="180" />
              <el-table-column prop="address" label="Address" />
            </el-table> -->
            <div class="table__header">
              <div class="text1">{{ store.getters.isProjectScreen == true ? '标段' : '项目' }}名称</div>
              <div class="text2">应急预案</div>
              <div class="text3">应急演练开展(次)</div>
            </div>
            <div class="table-tr">
              <div class="item" v-for="(item, index) in tableData" :key="index">
                <div class="te1">{{ item.name }}</div>
                <div class="te2" :class="{
        'color-a': item.complete == true,
        'color-b': item.complete == false,
      }">
                  {{ item.complete == true ? "已完成" : "未完成" }}
                </div>
                <div class="te3">
                  {{ item.count }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="changx-1" v-if="changx == 1">
        <ul class="list1">
          <li class="item1" v-if="satstate">已完成</li>
          <li class="item1-1" v-else>未完成</li>
          <li class="item2">
            <img v-if="satstate" src="../../assets/img/SafetyManagement/应急管理-应急预案.png" alt="" />
            <img v-else src="../../assets/img/SafetyManagement/应急管理预案-未完成.png" alt="" />
          </li>
          <li class="item3">
            <img v-if="satstate" src="../../assets/img/SafetyManagement/应急管理-应急预案-1.png" alt="" />
            <div class="img-weiwanc" v-else>
              <span>应急预案</span>
            </div>
          </li>
        </ul>
        <ul class="list2">
          <li class="item1">
            <span class="text1">{{ rehearsal }}</span>
            <span class="text2">次</span>
          </li>
          <li class="item2">
            <img src="../../assets/img/SafetyManagement/应急管理-应急演练开展1.png" alt="" />
          </li>
          <li class="item3">
            <img src="../../assets/img/SafetyManagement/应急管理-应急演练开展2.png" alt="" />
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>


<script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
const store = useStore();
let tableData = ref([]);
let emergencyPlanCompletionRate = ref(0);
let emergencyDrillTotal = ref(0);

onMounted(() => {
  if (store.getters.isSectionScreen == false) {
    // emergencyDrill();
    // emergencyPlan();
    campsec()
  } else {
    emergencyDrillSection();
    emergencyPlanSection();
  }
});
// 监听
let changx = ref(store.getters.isSectionScreen == true ? 1 : 0);
watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    if (store.getters.isSectionScreen == false) {
      // emergencyDrill();
      // emergencyPlan();
      campsec()
      changx.value = 0;
    } else {
      emergencyDrillSection({ projectId, sectionId });
      emergencyPlanSection({ projectId, sectionId });
      changx.value = 0;
    }
    if (store.state.sectionId) {
      changx.value = 1;
    }
  }
);

const emergencyDrill = () => {
  return new Promise(async (resolve, reject) => {
    let res = await api.emergencyDrill({
      projectId: store.state.projectId,
      sectionId: store.state.sectionId,
    });
    if (res.code == 200) {
      resolve(res.data);
    } else {
      reject(res.msg);
    }
  }).catch((err) => {
    console.log(err);
  });
};

const emergencyPlan = () => {
  return new Promise(async (resolve, reject) => {
    let res = await api.emergencyPlan({
      projectId: store.state.projectId,
      sectionId: store.state.sectionId,
    });
    resolve(res);
  }).catch((err) => {
    console.log(err);
  });
};
let rehearsal = ref(0); //演练次数
const emergencyDrillSection = () => {
  return new Promise(async (resolve, reject) => {
    let res = await api.emergencyDrillSection({
      projectId: store.state.projectId,
      sectionId: store.state.sectionId,
    });
    if (res.code == 200) {
      resolve(res.data);
      rehearsal.value = res.data;
    } else {
      reject(res.msg);
    }
  }).catch((err) => {
    console.log(err);
  });
};
let satstate = ref(true); //完成状态
const emergencyPlanSection = () => {
  return new Promise(async (resolve, reject) => {
    let res = await api.emergencyPlanSection({
      projectId: store.state.projectId,
      sectionId: store.state.sectionId,
    });
    satstate.value = res;
    resolve(res);
  }).catch((err) => {
    console.log(err);
  });
};

const campsec = () => {
  tableData.value = []
  emergencyDrillTotal.value = 0
  emergencyPlanCompletionRate.value = "0.00";
  Promise.all([emergencyDrill(), emergencyPlan()]).then(
    ([drillData, planData]) => {
      for (let item1 of drillData.simples) {
        for (let item2 of planData.lists) {
          if (item1.id === item2.id) {
            tableData.value.push({ ...item1, ...item2 });
          }
        }
        console.log('合并wanc数据：', tableData);

        emergencyDrillTotal.value = drillData.total;

        let finishCount = 0;
        let totalCount = 0;
        for (let item of tableData.value) {
          totalCount += item.count;
          if (item.complete == true) {
            finishCount += item.count;
          }
        }
        if (totalCount == 0) {
          emergencyPlanCompletionRate.value = "0.00";
        } else {
          // emergencyPlanCompletionRate.value = ((finishCount / totalCount) * 100).toFixed(2);
          emergencyPlanCompletionRate.value = (planData.completionRate * 100).toFixed(2);
        }
      }
    }
  );
};
</script>

<style lang="less" scoped>
.projectOverview {
  width: 28.75rem;
  //   height: 38.75rem;

  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/应急管理.png") center center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 15.9375rem;
    width: 100%;
    margin-top: 0.3125rem;
    padding: 1rem;
    box-sizing: border-box;

    //   position: absolute;
    >.changx-0 {
      >.top {
        width: 100%;
        display: flex;
        justify-content: space-evenly;

        >.left {
          display: flex;
          align-items: center;

          >img {
            width: 3rem;
          }

          .liability_statement {
            margin-left: 0.625rem;

            .text1 {
              text-align: left;
              color: #fff;
              height: 1.25rem;
              line-height: 1.25rem;
              font-weight: 700;
              font-size: 1.25rem;
            }

            .text2 {
              font-weight: 400;
              font-size: 0.75rem;
              color: #e2f2fb;
            }
          }
        }

        >.right {
          display: flex;
          align-items: center;

          >img {
            width: 3rem;
          }

          >.management_system {
            margin-left: 0.625rem;

            .text1 {
              text-align: left;
              color: #fff;
              height: 1.25rem;
              line-height: 1.25rem;
              font-weight: 700;
              font-size: 1.25rem;
            }

            .text2 {
              font-weight: 400;
              font-size: 0.75rem;
              color: #e2f2fb;
            }
          }
        }
      }

      >.bottom {
        margin-top: 0.625rem;
        width: 100%;
        height: 10rem;

        .form {
          width: 100%;
          height: 100%;
          // /deep/.el-table{

          //     --el-table-tr-bg-color: rgba(196, 75, 75, 0) !important;
          //     --el-table-bg-color:rgba(223, 0, 0, 0) !important;
          //     --el-table-header-bg-color: rgba(44, 164, 255, 0.322) !important;
          // }
          // /deep/.el-table__header-wrapper{
          //     height: 2rem !important;
          // }

          // /deep/.el-table__header{
          //     width: 100% !important;
          //     height: 2rem;
          // }
          .table__header {
            width: 100%;
            height: 2rem;
            background-color: rgba(44, 164, 255, 0.322);
            display: flex;

            .text1 {
              width: 14.125rem;
              height: 2rem;
              text-align: left;
              color: #2ca3ff;
              padding-left: 0.625rem;
              box-sizing: border-box;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text2 {
              width: 4.5rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }

            .text3 {
              width: 8.125rem;
              height: 2rem;
              color: #2ca3ff;
              text-align: center;
              line-height: 2rem;
              font-weight: 700;
              font-size: 0.875rem;
            }
          }

          .table-tr {
            width: 100%;
            overflow: hidden;
            overflow-y: auto;
            height: 8rem;

            .item {
              width: 100%;
              border-bottom: 0.0625rem solid rgba(44, 164, 255, 0.322);
              display: flex;

              >.te1 {
                width: 14.125rem;
                height: 2rem;
                text-align: left;
                color: #ffffff;
                padding-left: 0.625rem;
                box-sizing: border-box;
                line-height: 2rem;
                font-size: 0.875rem;
                overflow: hidden;
              }

              >.te2 {
                width: 4.5rem;
                height: 2rem;
                //   color: #ffffff;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }

              >.te3 {
                width: 8.125rem;
                height: 2rem;
                //   color: #ffffff;
                color: #d3deec;
                text-align: center;
                line-height: 2rem;
                font-size: 0.875rem;
              }
            }
          }
        }
      }
    }

    >.changx-1 {
      display: flex;
      align-items: center;
      justify-content: space-around;

      >.list1 {
        width: 8.25rem;
        height: 11.9375rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;

        >.item1 {
          font-weight: 700;
          font-size: 1.5rem;
          color: #3dffff;
          text-shadow: 0 0 16px #3dffff;
        }

        >.item1-1 {
          font-weight: 700;
          font-size: 1.5rem;
          color: #FF5046;
          text-shadow: 0 0 16px #FF5046;
        }

        >.item2 {
          >img {
            width: 6.5rem;
            height: 6.1875rem;
          }
        }

        >.item3 {
          >img {
            width: 8.25rem;
            height: 2rem;
          }

          >.img-weiwanc {
            width: 8.25rem;
            height: 2rem;
            background: url("../../assets/img/SafetyManagement/应急管理预案-未完成-1.png") center center no-repeat;
            background-size: 100% 100%;
            line-height: 2rem;
            text-align: center;

            >span {
              font-weight: 400;
              font-size: 0.875rem;
            }
          }
        }
      }

      >.list2 {
        width: 8.25rem;
        height: 11.9375rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-between;

        >.item1 {
          >.text1 {
            font-size: 2.25rem;
            font-weight: 700;
            color: #3ce7fe;
            text-shadow: 0 0 16px #2770ff;
          }

          >.text2 {
            font-size: 0.75rem;
            font-weight: 500;
            color: #3ce7fe;
            text-shadow: 0 0 16px #2770ff;
          }
        }

        >.item2 {
          >img {
            width: 6.5rem;
            height: 6.1875rem;
          }
        }

        >.item3 {
          >img {
            width: 8.25rem;
            height: 2rem;
          }
        }
      }
    }
  }
}

.color-a {
  color: #3dffff;
}

.color-b {
  color: #ff5046;
}
</style>