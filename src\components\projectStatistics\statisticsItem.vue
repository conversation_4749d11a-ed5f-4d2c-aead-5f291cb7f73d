<template>
  <div class="content">
    <div class="content-item-bg">
      <div class="content-item-bg-title">
        <div class="title-icon"></div>
        <div class="title-title">
          {{ title }}
        </div>
        <div class="title-bottom-line"></div>
        <div class="title-bottom-dot"></div>
      </div>
      <div v-show="isStatistics == 1" class="content-item-text item-tex1">
        <div class="text-title">已完成:</div>
        <div class="text-num">{{ num }}</div>
      </div>
      <div v-show="isStatistics == 1" class="content-item-text item-text2">
        <div class="text-title">总数:</div>
        <div class="text-num">{{ total }}</div>
      </div>
      <div v-show="isStatistics == 1" class="content-item-completionProgress">
        <div class="text-title">完成率:</div>
        <el-progress
          :percentage="progress || 0"
          :show-text="false"
          :stroke-width="6"
          :color="'linear-gradient(to right, #023164, #4798F7, #FFFFFF)'"
        />
        <div class="text-num">{{ (num / total * 100).toFixed(2) }}%</div>
      </div>
      <div class="content-status" v-show="isStatistics != 1" :class="status !== '已完成' ? 'content-status2' : ''">
          {{ status }}
      </div>
    </div>
  </div>
</template>
  
  <script setup>
import { onMounted, ref } from "vue";

const props = defineProps({
  title: String,
  amount: String,
  type: String,
  num: Number,
  // total: String,
  progress: Number,
  isStatistics: Number,
  status: String,
  total: Number,
});

onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>
  
  <style lang="less" scoped>
.content {
  width: 12.875rem;
  height: 4.5rem;
  .content-item-bg {
    background: linear-gradient(
      to right,
      rgba(71, 152, 247, 0.1),
      rgba(71, 152, 247, 0)
    );
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative;

    .content-item-bg-title {
      width: 100%;
      height: 2rem;
      display: flex;
      align-items: center;
      position: relative;
      .title-icon {
        background: url("../../assets/img/projectStatistics/icon1.png") center
          center no-repeat;
        background-size: 100% 100%;
        width: 2rem;
        height: 2rem;
      }
      .title-title {
        margin-left: 0.75rem;
        font-size: 0.875rem;
        font-weight: 400;
      }
      .title-bottom-line {
        position: absolute;
        bottom: 0;
        left: 2rem;
        right: 0.25rem;
        height: 0.0625rem;
        background: linear-gradient(
          to right,
          rgba(71, 152, 247, 1),
          rgba(2, 49, 100, 0.4)
        );
      }
      .title-bottom-dot {
        width: 0.25rem;
        height: 0.0625rem;
        position: absolute;
        bottom: 0;
        right: 0;
        background-color: #4798f7;
      }
    }
    .content-item-text {
      display: flex;
      align-items: center;
      .text-title {
        font-size: 0.75rem;
        color: #e2f2fb;
      }
      .text-num {
        font-size: 1rem;
        color: #3cc4fe;
        margin-left: 0.3125rem;
      }
    }
    .item-tex1 {
      position: absolute;
      top: 2.5rem;
      left: 0.75rem;
    }
    .item-text2 {
      position: absolute;
      top: 2.5rem;
      left: 6.8125rem;
    }
    .content-item-completionProgress {
      display: flex;
      align-items: center;
      position: absolute;
      top: 4rem;
      left: 0.75rem;
      .text-title {
        font-size: 0.75rem;
        color: #e2f2fb;
      }
      .text-num {
        font-size: 1rem;
        color: #3cc4fe;
        margin-left: .625rem;
      }
      :deep(.el-progress) {
        width: 5rem;
        margin-left: .3125rem;
        .el-progress-bar {
          .el-progress-bar__outer {
            background-color: rgba(61, 138, 255, 0.2);
          }
        }
      }
    }
    .content-status{
      font-size: 1.25rem;
      font-weight: 700;
      color: #3DFFFF;
      position: absolute;
      top: 3.125rem;
      left: 1rem;
    }
    .content-status2{
      color: #F7C034;
    }
  }
}
</style>
