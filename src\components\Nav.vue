<template>
  <div class="nav">
    <div class="nav-bg">
      <div class="nav-title"></div>
      <div
        class="nav-menu"
        :class="routerPath == '/' ? 'nav-menu-home' : 'nav-menu-other'"
      >
        <!-- <router-link class="r" to="/"></router-link>
        <router-link class="r" to="/safetyManagement"></router-link>
        <router-link class="r" to="/qualityControl"></router-link>
        <router-link class="r" to="/videoSurveillance"></router-link> -->
        <div
          class="r"
          :class="
            routerPath == '/' || routerPath == '/projectLevel' ? 'r1-s' : 'r1-n'
          "
          @click="to(0)"
        ></div>
        <div
          class="r"
          :class="routerPath == '/safetyManagement' ? 'r2-s' : 'r2-n'"
          @click="to(1)"
        ></div>
        <div
          class="r"
          :class="routerPath == '/qualityControl' ? 'r3-s' : 'r3-n'"
          @click="to(2)"
        ></div>
        <div
          class="r"
          :class="routerPath == '/videoSurveillance' ? 'r4-s' : 'r4-n'"
          @click="to(3)"
        ></div>
      </div>
      <div
        class="nav-left"
        v-if="store.getters.isProjectScreen && routerPath != '/'"
      >
        <div class="left-back" @click="back">
          <div class="back-bg">
            <div class="back-icon"></div>
            <div class="back-text">返回中心大屏</div>
          </div>
        </div>
        <div class="left-select">
          <div class="select-bg">
            <div class="top-title">选择项目：</div>
            <el-cascader
              ref="cascader"
              :props="props"
              v-model="projectIds"
              :options="projectList"
              @change="handleProjectChange"
              placeholder="请选择项目"
            />
          </div>
        </div>
        <div class="left-select">
          <div class="select-bg">
            <div class="top-title">选择标段：</div>
            <el-cascader
              :props="props"
              v-model="sectionIds"
              :options="sectionList"
              @change="handleSectionChange"
              placeholder="请选择标段"
            />
          </div>
        </div>
      </div>
      <div class="nav-system">
        <el-button
          size="small"
          text
          bg
          @click="openSystem"
          style="
            background-color: rgba(41, 88, 118, 0.8);
            color: #fff;
            margin-right: 1rem;
          "
        >
          进入系统
        </el-button>
        <el-text class="nav-system-text"
          >{{ userInfo.userName }}，欢迎您！</el-text
        >
        <!-- <el-text class="nav-system-s">|</el-text>
        <el-button type="primary" link class="logout-btn" @click="login">
          <el-icon :size="size" :color="color" class="logout-icon"> </el-icon>
          退出
        </el-button> -->
      </div>
      <div class="nav-time">
        <el-text class="nav-time-time">{{ dateHms }}</el-text>
        <el-text class="nav-time-s">|</el-text>
        <el-text class="nav-time-date">{{ dateWeek }} {{ dateYmd }}</el-text>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import api from "@/api/index";
import { useStore } from "vuex";
const router = useRouter();
let routerPath = ref("/");
const store = useStore();
const cascader = ref();

let dateHms = ref("");
let dateYmd = ref("");
let dateWeek = ref("");
let timeTrim = ref(null); // 定时器

const props = ref({ label: "name", value: "id" });
let projectList = ref([]);
let sectionList = ref([]);
const projectIds = ref([store.state.projectId]);
const sectionIds = ref([store.state.sectionId]);
const userInfo = ref({ userName: "" });

const openSystem = () => {
  localStorage.setItem("viewType", "home");
  localStorage.setItem("mainFrameToken", localStorage.getItem("token"));
  localStorage.setItem("mainFrameTokenType", "192");
  // window.location.href = 'http://*************:8182/iworks-web-new/#/home'
  window.location.href = "http://**************:8182/iworks-web-new/#/main";
  //window.location.href = 'http://111.50.67.58:8182/iworks-web-new/#/main'
};

const handleProjectChange = () => {
  console.log("zzz???", projectIds.value[0]);

  const projectId = projectIds.value[0];
  store.commit("updateZss", "zss");

  store.commit("updatePorjectId", projectId);
  store.commit("updateSectionId", null);
  sectionIds.value = [];
  // const item = projectList.value.find((item) => item.id == projectId);
  getSectionList({ projectId }, "");
};
const handleSectionChange = (sectionIds) => {
  console.log("zssss存标段ID", sectionIds[0]);
  store.commit("updateSectionId", sectionIds[0]);
};
const back = () => {
  router.push("/");
  // index.value = 0;
  projectIds.value = [];
  sectionIds.value = [];
  store.commit("updatePorjectId", "");
  store.commit("updateSectionId", "");
  console.log("返回-- ", projectIds.value);
};

const login = async () => {
  // let res = await api.login();
  // localStorage.setItem("token", res.data);
};

const to = (i) => {
  if (i == 0) {
    if (store.state.projectId != undefined && store.state.projectId != "") {
      router.push("/projectLevel");
      return;
    }
    router.push("/");
    // store.commit("updatePorjectId", "");
    // store.commit("updateSectionId", "");
    // projectIds.value = [];
    // sectionIds.value = [];
  } else if (i == 1) {
    router.push("/safetyManagement");
    // store.commit("updatePorjectId", "");
    // store.commit("updateSectionId", "");
  } else if (i == 2) {
    router.push("/qualityControl");
    // store.commit("updatePorjectId", "");
    // store.commit("updateSectionId", "");
  } else if (i == 3) {
    router.push("/videoSurveillance");
  }
};

const getProjectList = async () => {
  // let res = await api.getProjectList();
  // console.log(res);
  // projectList.value = res.data;
  Promise.all([getAllProjectList(), getProjectCoordinateList()]).then(
    ([projectData, addrData]) => {
      // console.log('合并后坐标projectData', projectData, 'addrData', addrData);
      projectList.value = [];
      for (let item1 of projectData) {
        for (let item2 of addrData) {
          if (item1.id === item2.projectId) {
            projectList.value.push({ ...item1, ...item2 });
          }
        }
      }
    }
  );
};

const getAllProjectList = async () => {
  let res = await api.getProjectList();
  let data = res.data;
  return data;
};

const getProjectCoordinateList = async () => {
  let res = await api.getProjectCoordinateList();
  let data = res.result;
  return data;
};

const getUserDetails = async () => {
  let res = await api.getUserDetails();
  userInfo.value = res.data;
};

const getSectionList = async (params, projectName) => {
  let res = await api.getSectionList(params);
  sectionList.value = res.data;
};

const getNowFormatDate = () => {
  var date = new Date();
  var month = date.getMonth() + 1;
  var strDate = date.getDate();

  var hours = date.getHours();
  var minutes = date.getMinutes();

  var seconds = date.getSeconds();

  if (month >= 1 && month <= 9) {
    month = "0" + month;
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = "0" + strDate;
  }
  if (hours >= 0 && hours <= 9) {
    hours = "0" + hours;
  }
  if (minutes >= 0 && minutes <= 9) {
    minutes = "0" + minutes;
  }

  if (seconds >= 0 && seconds <= 9) {
    seconds = "0" + seconds;
  }

  var a = new Array("日", "一", "二", "三", "四", "五", "六");
  var week = new Date().getDay();
  var weekDay = "星期" + a[week];

  dateWeek.value = weekDay;
  dateYmd.value = `${date.getFullYear()}年${month}月${strDate}日`;
  dateHms.value = `${hours}:${minutes}:${seconds}`;
};

onMounted(() => {
  timeTrim.value = setInterval(() => {
    getNowFormatDate();
  }, 1000);
  getProjectList();
  getUserDetails();

  const url = window.location.href;
  var object = {};
  if (url.indexOf("?") != -1) {
    var str = url.split("?")[1];
    var strs = str.split("&");
    for (var i = 0; i < strs.length; i++) {
      object[strs[i].split("=")[0]] = strs[i].split("=")[1];
    }
  }
  console.log("路由上面的参数：", object);

  const token = object.token;
  if (token != undefined) {
    localStorage.setItem("token", token);
  }

  projectIds.value = [store.state.projectId];
  console.log("projectIds=====", projectIds);

  if (store.state.projectId != null) {
    getSectionList({ projectId: store.state.projectId }, "");
  }

  if (routerPath.value == "/") {
    back();
  }

  // const token = window.location.href.split("token=")[1];
  // if (token != undefined) {
  //   localStorage.setItem("token", token);
  // }
});
onUnmounted(() => {
  clearInterval(timeTrim.value);
});
watch(
  () => [router.currentRoute.value, store.state.projectId],
  ([newValue, projectId]) => {
    console.log("newValue", newValue);
    routerPath.value = newValue.path;

    if (projectId != null) {
      projectIds.value = [store.state.projectId];
      console.log("这少时诵诗书", projectIds.value);
      // handleProjectChange();
      getSectionList({ projectId: store.state.projectId }, "");
    }
  },
  { immediate: true }
);
</script>

<!-- <script>
import { SwitchButton } from "@element-plus/icons-vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
export default {
  components: {
    SwitchButton,
  },
  name: "nav",
  props: {
    
  },
  setup() {
    console.log(router)
    // 判断当前路由是否是首页
    const isHomePage = router === '/';
    return {isHomePage};
  },

};
</script> -->

<style lang="less">
.nav {
  .nav-bg {
    background: url("../assets/img/center_top_bg.png") center center no-repeat;
    height: 6.25rem;
    width: 100%;
    background-size: 100% 100%;
    height: 100%;

    .nav-title {
      background: url("../assets/img/center_top_title.png") center center
        no-repeat;
      height: 6.25rem;
      width: 100%;
      background-size: 100% 100%;
    }

    .nav-menu {
      background: url("../assets/img/top-line-home.png") center center no-repeat;
      height: 2.825rem;
      width: 100%;
      background-size: 100% 100%;

      .r {
        display: inline-block;
        height: 100%;
        width: 6.875rem;
        margin: 0 0.9375rem;
        background-color: yellow;
      }

      .r1-s {
        background: url("../assets/img/nav/btn1-s.png") center center no-repeat;
        background-size: 100% 100%;
      }

      .r1-n {
        background: url("../assets/img/nav/btn1-n.png") center center no-repeat;
        background-size: 100% 100%;
      }

      .r2-s {
        background: url("../assets/img/nav/btn2-s.png") center center no-repeat;
        background-size: 100% 100%;
      }

      .r2-n {
        background: url("../assets/img/nav/btn2-n.png") center center no-repeat;
        background-size: 100% 100%;
      }

      .r3-s {
        background: url("../assets/img/nav/btn3-s.png") center center no-repeat;
        background-size: 100% 100%;
      }

      .r3-n {
        background: url("../assets/img/nav/btn3-n.png") center center no-repeat;
        background-size: 100% 100%;
      }

      .r4-s {
        background: url("../assets/img/nav/btn4-n.png") center center no-repeat;
        background-size: 100% 100%;
      }

      .r4-n {
        background: url("../assets/img/nav/btn4-n.png") center center no-repeat;
        background-size: 100% 100%;
      }
    }

    .nav-menu-home {
      background: url("../assets/img/top-line-home.png") center center no-repeat;
      background-size: 100% 100%;
      height: 2.825rem;
      width: 100%;
    }

    .nav-menu-other {
      background: url("../assets/img/top-line-other.png") center center
        no-repeat;
      background-size: 100% 100%;
      height: 2.825rem;
      width: 100%;
    }

    .nav-system {
      position: absolute;
      top: 3.125rem;
      right: 1.875rem;
      display: flex;
      align-items: center;

      .nav-system-text {
        color: #fff;
        font-size: 1.25rem;
      }

      .nav-system-s {
        color: #cdcdcd;
        font-size: 1.25rem;
        margin: 0 0.3125rem;
      }

      .logout-btn {
        font-size: 1.25rem;
        color: #fff;

        .logout-icon {
          margin-right: 0.3125rem;
        }
      }
    }

    .nav-time {
      position: absolute;
      top: 3.125rem;
      left: 1.875rem;
      display: flex;
      align-items: center;

      .nav-time-time {
        color: #fff;
        font-size: 1.25rem;
        font-weight: 800;
      }

      .nav-time-s {
        color: #cdcdcd;
        font-size: 1.25rem;
        margin: 0 0.625rem;
      }

      .nav-time-date {
        color: #fff;
        font-size: 1.25rem;
      }
    }

    .nav-left {
      position: absolute;
      top: 6.875rem;
      left: 1.875rem;
      display: flex;
      align-items: center;

      .left-back {
        cursor: pointer;
        background: url("../assets/img/nav/back-bg-color.png") center center
          no-repeat;
        background-size: 100% 100%;
        height: 2rem;
        width: 7.75rem;

        .back-bg {
          background: url("../assets/img/nav/back-bg.png") center center
            no-repeat;
          background-size: 100% 100%;
          height: 100%;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .back-icon {
            background: url("../assets/img/nav/back-icon.png") center center
              no-repeat;
            background-size: 100% 100%;
            height: 1rem;
            width: 1rem;
          }

          .back-text {
            font-size: 0.875rem;
            color: #fff;
            margin-left: 0.5rem;
          }
        }
      }

      .left-select {
        cursor: pointer;
        background: url("../assets/img/nav/select-bg-color.png") center center
          no-repeat;
        background-size: 100% 100%;
        height: 2rem;
        width: 15rem;
        margin-left: 1rem;

        .select-bg {
          background: url("../assets/img/nav/back-bg.png") center center
            no-repeat;
          background-size: 100% 100%;
          height: 100%;
          width: 100%;
          display: flex;
          justify-content: flex-end;
          align-items: center;

          .top-title {
            font-size: 0.875rem;
            color: #fff;
            width: 8.125rem;
            margin-right: -1.25rem;
          }

          .el-cascader,
          .el-tooltip__trigge,
          .el-tooltip__trigger {
            .el-input,
            .el-input--suffix {
              .el-input__wrapper {
                background-color: rgba(0, 0, 0, 0.1);
                box-shadow: 0 0 0 0;
                font-size: 1rem;

                .el-input__inner {
                  color: #fff;
                }
              }
            }
          }
        }
      }
    }
  }
}

.el-popper {
  max-width: 21.25rem;
}
</style>
