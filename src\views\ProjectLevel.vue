<template>
  <div class="controller">
    <investment class="investment"></investment>
    <workplaceSection
      v-if="store.getters.isSectionScreen"
      class="workplaceSection"
    ></workplaceSection>
    <workplace v-else class="workplace"></workplace>
    <personInfo v-if="store.getters.isSectionScreen" class="personInfo"></personInfo>
    <projectStatistics
      v-else
      class="projectStatistics"
      :type='0'
    ></projectStatistics>
    <videoList class="videoList"></videoList>
    <checkPicture class="checkPicture"></checkPicture>
    <projectProfile class="projectProfile"></projectProfile>
  </div>
</template>
  
<script setup>
import { ref, onMounted } from "vue";
import investment from "../components/scheduleManagement/investment.vue";
import workplace from "../components/workplace/index.vue";
import projectStatistics from "../components/projectStatistics/index.vue";
import videoList from "../components/videoList/index.vue";
import checkPicture from "../components/checkPicture/index.vue";
import projectProfile from "../components/projectProfile/index.vue";
import workplaceSection from "../components/workplaceSection/index.vue";
import personInfo from "../components/personInfo/index.vue";
import { useStore } from "vuex";

const store = useStore();

// let sectionId2 = ref(null);
const count = ref(1);
const onClick = () => {
  count.value += 1;
};

onMounted(() => {
  // sectionId2.value = store.state.sectionId;
});

// watch(
//   () => [store.state.sectionId],
//   ([sectionId]) => {
//     sectionId2.value = sectionId;
//   }
// );
</script>
  
<style scoped lang='less'>
.controller {
  position: relative;

  .investment {
    position: absolute;
    top: 0.625rem;
    left: 1.25rem;
  }
  .workplace {
    position: absolute;
    top: 28.25rem;
    left: 1.25rem;
  }
  .workplaceSection {
    position: absolute;
    top: 28.25rem;
    left: 1.25rem;
  }
  .projectStatistics {
    position: absolute;
    top: 0.625rem;
    right: 1.25rem;
  }
  .personInfo {
    position: absolute;
    top: 0.625rem;
    right: 1.25rem;
  }
  .videoList {
    position: absolute;
    top: 19.0625rem;
    right: 1.25rem;
  }
  .checkPicture {
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    top: 0.625rem;
  }
  .projectProfile {
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    top: 42.9375rem;
  }
}
</style>