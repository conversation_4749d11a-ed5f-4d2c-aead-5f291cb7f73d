<template>
  <div v-if="isMobile == false" class="root">
    <div class="root-center"></div>
    <bigNav class="nav"></bigNav>
    <router-view />
  </div>
  <div v-if="isMobile == true" class="rootMobile">
    <!-- <videoListView></videoListView> -->
    <!-- <videoList ></videoList> -->
    <router-view />
  </div>
</template>

<script>
import { reactive, toRefs, onMounted } from "vue";
import bigNav from "../src/components/Nav.vue";
import nativeMethods from "../src/utils/nativeMethod";
import api from "@/api/index";
const { exit } = nativeMethods;
export default {
  name: "",
  components: {
    bigNav,
  },
  setup() {
    const data = reactive({
      isMobile: true,
    });
    onMounted(() => {
      isMobile();
      window.callbackPage = () => {
        const { hash } = window.location;
        exit(hash);
      };
      setInterval(() => {
        api.getUserDetails();
      }, 60000);
    });
    const isMobile = () => {
      // let flag = navigator.userAgent.match(
      //   /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      // );
      // console.log('是什么手机？',flag,navigator.userAgent);

      const userAgent = navigator.userAgent;
      const mobileKeywords = [
        "Android",
        "iPhone",
        "iPad",
        "Windows Phone",
        "BlackBerry",
        "Opera Mini",
        "Symbian",
        "Kindle",
        "Mobile",
      ];

      const regex = new RegExp(mobileKeywords.join("|"), "i");

      data.isMobile = regex.test(userAgent);

      if (data.isMobile == true) {
        const url = window.location.href;
        var object = {};
        if (url.indexOf("?") != -1) {
          var str = url.split("?")[1];
          var strs = str.split("&");
          for (var i = 0; i < strs.length; i++) {
            object[strs[i].split("=")[0]] = strs[i].split("=")[1];
          }
        }
        console.log("路由上面的参数：", object);

        const token = object.token;
        if (token != undefined) {
          localStorage.setItem("token", token);
        }
      }
    };
    return {
      ...toRefs(data),
    };
  },
};
</script>


<style>
ul,
p,
li {
  list-style: none;
  margin: 0;
  padding: 0;
}

*::-webkit-scrollbar {
  display: none;
}
html {
  font-size: 1.7vw;
  /* width: 100vw; */
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  padding: 0;
  width: 100vw;
  height: 67.5rem;
  /* height: 100vh; */
  box-sizing: border-box;
  overflow-x: auto;
  background-color: #000;
  color: #fff;
}

@media screen and (max-width: 768px) {
  #app {
    font-family: Avenir, Helvetica, Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-align: center;
    color: #2c3e50;
    padding: 0;
    width: 100vw;
    height: 100vh;
    box-sizing: border-box;
    overflow-x: hidden;
    background-color: #000;
    color: #fff;
  }
}
.rootMobile {
  width: 100%;
  height: 100%;
}

body {
  margin: 0;
}

.root {
  width: 100%;
  height: 100%;
  background: url("./assets//img/enterprise_bg.png") center center no-repeat;
  background-size: 100% 100%;
  position: relative;
  z-index: 0;
}
.root-center {
  width: 57.3vw;
  height: 50vw;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: url("./assets//img/enterprise_bgCenter.png") center center
    no-repeat;
  background-size: 100% 100%;
  z-index: -1;
}
.nav {
  position: relative;
}
</style>
