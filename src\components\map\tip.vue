<template>
  <div class="tip">
    <div class="tip-title">{{ title }}</div>
    <div class="tip-btn" @click="pushProjectLevel">进入项目</div>
  </div>
</template>


<script setup>
import { onMounted, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
const store = useStore();
const router = useRouter();
const props = defineProps({
  title: String,
  projectId: String
});

const pushProjectLevel = () => {
  store.commit("updatePorjectId", props.projectId);
  router.push('/projectLevel');
  // router.push({ name:'/projectLevel', params: { projectId: props.projectId }});
}

onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>
  
<style lang="less" scoped>
.tip {
  // width: 12.5rem;
  // height: 3.75rem;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  flex-direction: column;
  .tip-title {
    font-size: 0.875rem;
    color: #000;
  }
  .tip-btn {
    font-size: 0.875rem;
    color: #fff;
    background-color: #007aff;
    padding: 0.125rem 0.25rem;
    border-radius: 0.3125rem;
    width: 7.5rem;
    height: 2rem;
    text-align: center;
    line-height: 2rem;
    margin-top: 0.5rem;
  }
}
</style>