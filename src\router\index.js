import { createRouter, createWebHashHistory } from "vue-router";
import ScheduleManagement from "../views/ScheduleManagement.vue";
import ProjectLevel from "../views/ProjectLevel.vue";

const routes = [
  {
    path: "/",
    name: "scheduleManagement",
    component: ScheduleManagement,
  },
  {
    path: "/safetyManagement",
    name: "safetyManagement",
    component: () => import("../views/SafetyManagement.vue"),
  },
  {
    path: "/qualityControl",
    name: "qualityControl",
    component: () => import("../views/QualityControl.vue"),
  },
  {
    path: "/videoSurveillance",
    name: "videoSurveillance",
    component: () => import("../views/VideoSurveillance.vue"),
  },
  {
    path: "/projectLevel",
    name: "projectLevel",
    component: ProjectLevel,
  },
  {
    path: "/VideoListView",
    name: "VideoListView",
    component: () => import("../views/VideoListView.vue"),
  },
  {
    path: "/VideoList",
    name: "VideoList",
    component: () => import("../views/VideoList.vue"),
  },
  {
    path: "/ProgressManagementView",
    name: "ProgressManagementView",
    component: () => import("../views/ProgressManagementView.vue"),
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// 路由守卫：处理token路径
router.beforeEach((to, from, next) => {
  // 检查路径是否包含token
  if (to.path.startsWith("/token=")) {
    // 提取token
    const token = to.path.substring(7); // 去掉 '/token=' 前缀

    // 存储token
    localStorage.setItem("token", token);
    console.log("Token extracted and stored:", token);

    // 重定向到首页
    next("/");
    return;
  }

  next();
});

export default router;
