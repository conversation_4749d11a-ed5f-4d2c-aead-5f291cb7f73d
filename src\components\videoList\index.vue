<template>
  <div class="videoList">
    <!-- <div @click="addClick">add: {{ count }}</div> -->
    <div class="videoList-title"></div>
    <div class="videoList-content">
        <item class="content-item" v-for="(cell, index) in videos" :key="cell.cameraIndexCode" :item="cell" :index="index" />
    </div>
  </div>
</template>
  
  
  <script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
import item from './item.vue'
const store = useStore();

const props = defineProps({
  type: Number,
});
let videos = ref();
const count = ref(0)
// const addClick = () => {
//   count.value++
//   store.commit('updateZss',count.value)
// }

onMounted(() => {
  videoList({ projectId: store.state.projectId, sectionId: store.state.sectionId });
});

watch(() => [store.state.projectId, store.state.sectionId,store.state.zss],([projectId, sectionId]) => {
  console.log('监听了视频列表接口：')    
  videoList({ projectId, sectionId });
});

const videoList = async (params) => {
  let res = await api.videoList(params)
  let videoList = res.data

  const promises = [];
  videoList.map((item) => {
    const promise =  videoLink( item );
    promises.push(promise);
  })
  await Promise.all(promises); // 等待所有请求完成
  videos.value = videoList
}

const videoLink = async (item) => {
  let res = await api.videoLink(item.cameraIndexCode);
  item.url = res.data.url
};

</script>
  
  <style lang="less" scoped>
.videoList {
  width: 28.75rem;
  height: 38.75rem;

  .videoList-title {
    background: url("../../assets/img/videoList/title.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }
  .videoList-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 36.25rem;
    overflow: auto;
    padding: 16px;
    .content-item{
        width: calc(100%);
        height: 14rem;
        margin-bottom: 1rem;
    }
  }
}
</style>
