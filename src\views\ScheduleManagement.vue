<!-- 进度管理 -->
<template>
  <div class="controller">
    <investment class="investment"></investment>
    <workplace class="workplace"></workplace>
    <projectStatistics class="projectStatistics" type="1"></projectStatistics>
    <projectOverview class="projectOverview"></projectOverview>
    <mapView class="map"></mapView>
  </div>
</template>

<!-- <template>  <div>{{ count }}</div>  <button @click="onClick">    增加 1  </button>
</template>
<script>
import { ref } from 'vue';
export default {  // 注意这部分  
  setup() {    
    let count = ref(1);    
    const onClick = () => {      
      count.value += 1;    
    };    
    return {      
      count,      
      onClick,    
    };  
  },  
}
</script> -->


<script setup>
import { ref } from "vue";
import investment from "../components/scheduleManagement/investment.vue";
import workplace from '../components/workplace/index.vue'
import projectStatistics from '../components/projectStatistics/index.vue'
import projectOverview from '../components/projectOverview/index.vue'
import mapView from '../components/map/index.vue'

const count = ref(1);
const onClick = () => {
  count.value += 1;
};
</script>


<!-- 
<script>
import { reactive, toRefs, onBeforeMount, onMounted, ref } from 'vue'
import api from "@/api";
import InvestmentProgress from '../components/scheduleManagement/InvestmentProgress.vue'

export default {
  components: {
    InvestmentProgress
  },
  name: '',
  setup() {
    const investmentProgressPro = ref('80')

    console.log('1-开始创建组件-setup')
    const test = async () => {
      console.log("z");

      let data = await api.test();
      console.log(data);
    };
    
    const data = reactive({})
    onBeforeMount(() => {
      console.log('2.组件挂载页面之前执行----onBeforeMount')
    })
    onMounted(() => {
      console.log('3.-组件挂载到页面之后执行-------onMounted')
      test()
    })
    return {
      ...toRefs(data),investmentProgressPro
    }
  },
}

</script> -->
<style scoped lang='less'>
.controller {
  position: relative;
  
  .investment {
    position: absolute;
    top: 0.625rem;
    left: 1.25rem;
  }
  .workplace{
    position: absolute;
    top: 28.25rem;
    left: 1.25rem;
  }
  .projectStatistics{
    position: absolute;
    top: 0.625rem;
    right: 1.25rem;
  }
  .projectOverview{
    position: absolute;
    top: 19.0625rem;
    right: 1.25rem;
  }
  .map{
    position: absolute;
    left: 50%;
    transform: translate(-50%, 0);
    top: 3.125rem
  }
}
</style>