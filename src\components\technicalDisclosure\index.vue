<template>
  <div class="technicalDisclosure">
    <div class="technicalDisclosure-title">
      <!-- <div class="top-title">选择标段：</div>
      <el-cascader v-model="value" :options="options"  /> -->
    </div>
    <div class="technicalDisclosure-content" id="charts"></div>
  </div>
</template>
  
  
  <script setup>
import { onMounted, ref, watch } from "vue";
import * as echarts from "echarts";
import api from "@/api/index";
import { useStore } from "vuex";
let store = useStore();
const props = defineProps({
  type: Number,
});
const value = ref([]);
const options = [
  {
    value: "guide",
    label: "Guide",
    children: [
      {
        value: "disciplines",
        label: "Disciplines",
        children: [
          {
            value: "consistency",
            label: "Consistency",
          },
          {
            value: "feedback",
            label: "Feedback",
          },
          {
            value: "efficiency",
            label: "Efficiency",
          },
          {
            value: "controllability",
            label: "Controllability",
          },
        ],
      },
      {
        value: "navigation",
        label: "Navigation",
        children: [
          {
            value: "side nav",
            label: "Side Navigation",
          },
          {
            value: "top nav",
            label: "Top Navigation",
          },
        ],
      },
    ],
  },
  {
    value: "component",
    label: "Component",
    children: [
      {
        value: "basic",
        label: "Basic",
        children: [
          {
            value: "layout",
            label: "Layout",
          },
          {
            value: "color",
            label: "Color",
          },
          {
            value: "typography",
            label: "Typography",
          },
          {
            value: "icon",
            label: "Icon",
          },
          {
            value: "button",
            label: "Button",
          },
        ],
      },
      {
        value: "form",
        label: "Form",
        children: [
          {
            value: "radio",
            label: "Radio",
          },
          {
            value: "checkbox",
            label: "Checkbox",
          },
          {
            value: "input",
            label: "Input",
          },
          {
            value: "input-number",
            label: "InputNumber",
          },
          {
            value: "select",
            label: "Select",
          },
          {
            value: "cascader",
            label: "Cascader",
          },
          {
            value: "switch",
            label: "Switch",
          },
          {
            value: "slider",
            label: "Slider",
          },
          {
            value: "time-picker",
            label: "TimePicker",
          },
          {
            value: "date-picker",
            label: "DatePicker",
          },
          {
            value: "datetime-picker",
            label: "DateTimePicker",
          },
          {
            value: "upload",
            label: "Upload",
          },
          {
            value: "rate",
            label: "Rate",
          },
          {
            value: "form",
            label: "Form",
          },
        ],
      },
      {
        value: "data",
        label: "Data",
        children: [
          {
            value: "table",
            label: "Table",
          },
          {
            value: "tag",
            label: "Tag",
          },
          {
            value: "progress",
            label: "Progress",
          },
          {
            value: "tree",
            label: "Tree",
          },
          {
            value: "pagination",
            label: "Pagination",
          },
          {
            value: "badge",
            label: "Badge",
          },
        ],
      },
      {
        value: "notice",
        label: "Notice",
        children: [
          {
            value: "alert",
            label: "Alert",
          },
          {
            value: "loading",
            label: "Loading",
          },
          {
            value: "message",
            label: "Message",
          },
          {
            value: "message-box",
            label: "MessageBox",
          },
          {
            value: "notification",
            label: "Notification",
          },
        ],
      },
      {
        value: "navigation",
        label: "Navigation",
        children: [
          {
            value: "menu",
            label: "Menu",
          },
          {
            value: "tabs",
            label: "Tabs",
          },
          {
            value: "breadcrumb",
            label: "Breadcrumb",
          },
          {
            value: "dropdown",
            label: "Dropdown",
          },
          {
            value: "steps",
            label: "Steps",
          },
        ],
      },
      {
        value: "others",
        label: "Others",
        children: [
          {
            value: "dialog",
            label: "Dialog",
          },
          {
            value: "tooltip",
            label: "Tooltip",
          },
          {
            value: "popover",
            label: "Popover",
          },
          {
            value: "card",
            label: "Card",
          },
          {
            value: "carousel",
            label: "Carousel",
          },
          {
            value: "collapse",
            label: "Collapse",
          },
        ],
      },
    ],
  },
  {
    value: "resource",
    label: "Resource",
    children: [
      {
        value: "axure",
        label: "Axure Components",
      },
      {
        value: "sketch",
        label: "Sketch Templates",
      },
      {
        value: "docs",
        label: "Design Documentation",
      },
    ],
  },
];
// const handleChange = (value) => {
//   console.log(value);
// };

onMounted(() => {
  console.log("组件已挂载");
  // loadCharts();
  disclosurex();
});
// 监听
watch(
  () => [store.state.projectId,store.state.sectionId],
  (projectId, sectionId) => {
      disclosurex();
  }
);
// 更新柱状图
const disclosurex = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };

  api
    .disclosure(params)
    .then((res) => {
      console.log(res.data, "技术交底请求成功");
      let data = res.data;
      loadCharts(data);
    })
    .catch((err) => {
      console.log(err, "技术交底请求失败");
    });
};
const loadCharts = (data) => {
  let shexiangElement = document.getElementById("charts");
  // 销毁已有的 echarts 实例
  echarts.dispose(shexiangElement);
  var chartDom = document.getElementById("charts");
  var myChart = echarts.init(chartDom);
  var option;
  console.log(data,'sabdjkbakjsdbdkjabdjahbsd');
  option = {
    legend: {
      textStyle: {
        color: "rgba(255,255,255,0.6)", // 修改图例文本颜色
        fontSize: 12, // 修改图例文本字体大小
        fontWeight: 400,
      },
    },
    tooltip: {},
    dataset: {
      source: [
        ["product", "计划次数", "实际次数"],
        [
          "设计交底",
          data?.designPlanCount ? data?.designPlanCount : 0,
          data?.designActualCount ? data?.designActualCount : 0,
        ],
        [
          "分部工程交底",
          data?.subProjectPlanCount ? data?.subProjectPlanCount : 0,
          data?.subProjectActualCount ? data?.subProjectActualCount : 0,
        ],
        [
          "施工工艺交底",
          data?.constructionPlanCount ? data?.constructionPlanCount : 0,
          data?.constructionActualCount ? data?.constructionActualCount : 0,
        ],
      ],
    },
    xAxis: {
      type: "category",
    },
    yAxis: {
      name: "次数",
      nameTextStyle: { padding: [0, 0, 0, -30] },
    },
    // Declare several bar series, each will be mapped
    // to a column of dataset.source by default.
    series: [
      {
        type: "bar",
        barWidth: 15,
        itemStyle: {
          color: "#2CA3FF",
        },
      },
      {
        type: "bar",
        barWidth: 15,
        itemStyle: {
          color: "#3DFFFF",
        },
      },
    ],
    textStyle: { color: "#D3DEEC" },
  };
  option && myChart.setOption(option);
};
</script>
  
  <style lang="less" scoped>
.technicalDisclosure {
  width: 28.75rem;
  height: 18.4375rem;

  .technicalDisclosure-title {
    background: url("../../assets/img/technicalDisclosure/title.png") center
      center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    .top-title {
      font-size: 0.875rem;
      color: #fff;
    }
    :deep(.el-cascader, .el-tooltip__trigge, .el-tooltip__trigger) {
      // background-color: red;
      .el-input__wrapper {
        background-color: rgba(0, 0, 0, 0.1);
        box-shadow: 0 0 0 0;
      }
    }
  }

  .technicalDisclosure-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 15.9375rem;
    width: 100%;
    margin-top: .3125rem;
  }
}
</style>