<template>
  <div class="controller">
    <div class="top">
      <div class="top-title">视频监控</div>
    </div>
    <div class="content">
      <div class="left">
        <div class="left-search">
          <el-input v-model="searchKey" placeholder="请输入关键词搜索" clearable />
        </div>
        <div class="left-content">
          <el-tree style="max-width: 600px" :data="projectList" :props="defaultProps" :load="loadNode" lazy
            @node-click="handleNodeClick" />
        </div>
      </div>
      <div class="line"></div>
      <div class="right">
        <div class="top-tip"></div>
        <div class="right-content">
          <div class="item" v-for="(item, index) in videos" :key="item.url" @dblclick="handleDoubleVideoClick(index)">
            <div class="item-top">
              <div class="top-icon"></div>
              <div class="top-title">{{ item.name }}</div>
            </div>
            <div class="item-video">
              <!-- <el-image
                v-show="item.url == null"
                style="width: 100%; height: 100%"
                src=""
              /> -->
              <div class="video" v-show="item.url != null">
                <videoer style="width: 100%; height: 100%" class="!w-100% !h-100% pointer-events-none"
                  :playUrl="item.url" :idStr="index + 'id'" :changeFlg="changeFlg">
                </videoer>
                <div class="video-bg"></div>
              </div>
              <div class="video-bottom">
                <span v-for="icon in iconList" :key="icon.name" class="flex items-center"
                  @mousedown="controlCamera(icon, item.cameraIndexCode, 0)"
                  @mouseup="controlCamera(icon, item.cameraIndexCode, 1)">
                  <img class="w-5 h-5" :src="getAssetURL(icon.name)"
                    style="width: 32px; height: 32px; margin-top: 2px" />
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="leftTopIcon"></div>
    <div class="rightTopIcon"></div>
    <div class="leftBottomIcon"></div>
    <div class="rightBottomIcon"></div>
    <div class="close" @click="close"></div>
    <el-dialog v-model="isFullScreen" :title="item.name" :destroy-on-close="true" :close-on-click-modal="true"
      fullscreen :center="false" :before-close="() => {
        isFullScreen = false;
      }
        " modal-class="fullScreen" custom-class="fullScreen">
      <div class="video-big" style="width: 80%; transform: translateX(10%);  height: calc(57.5rem - 2.5rem)">
        <videoer style="width: 100%; height: 100%" class="!w-100% !h-100% pointer-events-none" :playUrl="selectUrl"
          :idStr="indexSelect + '-' + indexSelect" :changeFlg="changeFlg">
        </videoer>
        <div class="absolute bottom-1 flex w-full justify-center" style="margin-top: -40px; position: absolute;">
          <span v-for="icon in iconList" :key="icon.name" class="flex items-center"
            @mousedown="controlCamera(icon, item.cameraIndexCode, 0)"
            @mouseup="controlCamera(icon, item.cameraIndexCode, 1)">
            <img class="w-5 h-5" :src="getAssetURL(icon.name)" style="width: 32px; height: 32px; margin-top: 2px" />
          </span>
        </div>
      </div>
    </el-dialog>
    <!-- <el-dialog
    v-model="isFullScreen"
    custom-class="dialogForm"
    :title="item.name"
    :destroy-on-close="true"
    :close-on-click-modal="true"
    fullscreen="true"
   
  >
    <div class="!w-full !h-full relative" v-if="item.url">
      <videoer
        class="!w-full !h-full pointer-events-none"
        :playUrl="item.url"
        :idStr="indexSelect + '-' + indexSelect"
      ></videoer>
      <div class="absolute bottom-1 flex w-full justify-center">
        <div class="absolute bottom-1 flex w-full justify-center" style="margin-top: -40px; position: absolute;">
          <span
            v-for="icon in iconList"
            :key="icon.name"
            class="flex items-center"
            @mousedown="controlCamera(icon, item.cameraIndexCode, 0)"
            @mouseup="controlCamera(icon, item.cameraIndexCode, 1)"
          >
            <img
              class="w-5 h-5"
              :src="getAssetURL(icon.name)"
              style="width: 32px; height: 32px; margin-top: 2px"
            />
          </span>
        </div>
      </div>
    </div>
  </el-dialog> -->
  </div>
</template>

<script setup>
import { ref, onMounted, toRaw } from "vue";
import { useRouter, useRoute } from "vue-router";
import videoer from "../components/videoer/videoer.vue";
import api from "@/api/index";
// import "../assets/js/h5player.min.js";
const router = useRouter();
const searchKey = ref("");
const videos = ref([]);
const changeFlg = ref(false);
const isFullScreen = ref(false);
const item = ref({ name: '', url: '', cameraIndexCode: '' })
const indexSelect = ref(0);
const selectUrl = ref('');

const iconList = ref([
  {
    type: "ZOOM_OUT",
    icon: "../assets/img/video_minus.png",
    name: "缩小",
  },
  {
    type: "ZOOM_IN",
    icon: "../assets/img/video_add.png",
    name: "放大",
  },
  {
    type: "LEFT_UP",
    icon: "../assets/img/video_left_top.png",
    name: "左上",
  },
  {
    type: "LEFT_DOWN",
    icon: "../assets/img/video_left_bottom.png",
    name: "左下",
  },
  {
    type: "UP",
    icon: "../assets/img/video_top.png",
    name: "上",
  },
  {
    type: "DOWN",
    icon: "../assets/img/video_bottom.png",
    name: "下",
  },
  {
    type: "LEFT",
    icon: "../assets/img/video_left.png",
    name: "左",
  },
  {
    type: "RIGHT",
    icon: "../assets/img/video_right.png",
    name: "右",
  },
  {
    type: "RIGHT_UP",
    icon: "../assets/img/video_right_top.png",
    name: "右上",
  },
  {
    type: "RIGHT_DOWN",
    icon: "../assets/img/video_right_bottom.png",
    name: "右下",
  },
]);

const playUrl = ref("ws://111.51.70.167:559/openUrl/upb6l5m");
const data = [
  {
    label: "Level one 1",
    children: [
      {
        label: "Level two 1-1",
        children: [
          {
            label: "Level three 1-1-1",
          },
        ],
      },
    ],
  },
  {
    label: "Level one 2",
    children: [
      {
        label: "Level two 2-1",
        children: [
          {
            label: "Level three 2-1-1",
          },
        ],
      },
      {
        label: "Level two 2-2",
        children: [
          {
            label: "Level three 2-2-1",
          },
        ],
      },
    ],
  },
  {
    label: "Level one 3",
    children: [
      {
        label: "Level two 3-1",
        children: [
          {
            label: "Level three 3-1-1",
          },
        ],
      },
      {
        label: "Level two 3-2",
        children: [
          {
            label: "Level three 3-2-1",
          },
        ],
      },
    ],
  },
];

const defaultProps = {
  children: "children",
  label: "name",
  value: "id",
  isLeaf: "leaf",
};
const projectList = ref([]);

const getAssetURL = (name) => {
  if (name == '上') {
    return new URL(`../assets/img/video_top.png`, import.meta.url).href;
  } else if (name == '下') {
    return new URL(`../assets/img/video_bottom.png`, import.meta.url).href;
  } else if (name == '左') {
    return new URL(`../assets/img/video_left.png`, import.meta.url).href;
  } else if (name == '右') {
    return new URL(`../assets/img/video_right.png`, import.meta.url).href;
  } else if (name == '左上') {
    return new URL(`../assets/img/video_left_top.png`, import.meta.url).href;
  } else if (name == '左下') {
    return new URL(`../assets/img/video_left_bottom.png`, import.meta.url).href;
  } else if (name == '右上') {
    return new URL(`../assets/img/video_right_top.png`, import.meta.url).href;
  } else if (name == '右下') {
    return new URL(`../assets/img/video_right_bottom.png`, import.meta.url).href;
  } else if (name == '缩小') {
    return new URL(`../assets/img/video_minus.png`, import.meta.url).href;
  } else if (name == '放大') {
    return new URL(`../assets/img/video_add.png`, import.meta.url).href;
  }
  return new URL(image, import.meta.url).href;
  // return new URL(`../assets/img/video_top.png`, import.meta.url).href;
  // return new URL(`../assets/img/${image}`, import.meta.url).href;
};

const controlCamera = (icon, code, type) => {
  let params = {
    cameraIndexCode: code,
    action: type,
    command: icon.type,
  };
  api.videoControl(params).then((res) => {
    console.log(res);
  });
};

const handleDoubleVideoClick = (index) => {
  console.log('双击：', index);
  item.value = videos.value[index];
  selectUrl.value = videos.value[index].url;
  indexSelect.value = index
  isFullScreen.value = true
}

onMounted(() => {
  getProjectList();
  // videoList({ projectId: "", sectionId: "" });
});

const getProjectList = async () => {
  let res = await api.getProjectListHasAuth();
  console.log(res);
  projectList.value = res.data;

  
  for (let index = 0; index < res.data.length; index++) {
    const element = res.data[index];

    let res2 = await api.getSectionListHasAuth({ projectId: element.id });
    let sectionIds = []
    res2.data.forEach((obj) => {
      sectionIds.push(obj.id)
    });
    // videoList({ projectId: node.data.id, sectionId: "", sectionIds: sectionIds });

    let res3 = await api.videoList({ projectId: element.id, sectionId: "", sectionIds: sectionIds });
    console.log("监控：", res3);
    let videoRes = res3.data;

    if (videoRes.length > 0) {
      videos.value = res3.data;
      videos.value.map((item) => {
        videoLink(item);
      })

      break;
    }
  }
};

const getSectionList = async (projectId) => {
  let res = await api.getSectionListHasAuth({ projectId });
  console.log(res);
  projectList.value = res.data;
};

const loadNode = async (node, resolve) => {
  console.log(node);
  if (node.level === 0) {
    // return resolve([]);
  }
  if (node.level > 1) return resolve([]);

  let res = await api.getSectionListHasAuth({ projectId: node.data.id });
  let sectionIds = []
  res.data.forEach((obj) => {
    obj.leaf = true;
    obj.isSection = true;
    sectionIds.push(obj.id)
  });
  node.data.sectionIds = sectionIds
  videoList({ projectId: node.data.id, sectionId: "", sectionIds: sectionIds });

  resolve(res.data);
};

const videoList = async (params) => {
  let res = await api.videoList(params);
  console.log("监控：", res);
  videos.value = res.data;

  videos.value.map((item) => {
    videoLink(item);
  })
};

const videoLink = async (item) => {
  let res = await api.videoLink(item.cameraIndexCode);
  item.url = res.data.url
};

const querySearch = (queryString, cb) => {
  // const results = queryString
  //   ? restaurants.value.filter(createFilter(queryString))
  //   : restaurants.value
  // call callback function to return suggestions
  // cb(results)
  console.log(queryString, cb);
};

const handleNodeClick = (data) => {
  console.log("点击：", data);
  // let dic = toRaw(data)
  // console.log('点击1', dic)
  // let dic2 = JSON.parse(JSON.stringify(data))
  // console.log('点击1', dic2, dic2.zss)
  if (data.isSection == true) {
    videoList({ projectId: data.projectId, sectionId: data.id, sectionIds: data.sectionIds });
  } else {
    videoList({ projectId: data.id, sectionId: "", sectionIds: data.sectionIds });
  }
};

const close = () => {
  console.log("关闭视频监控", router);
  // router.push('/');
  // 判断是否有上一级路由？有则回退：没有则跳转首页
  window.history.state.back ? router.go(-1) : router.push("/");
};
</script>

<!-- <script>
import { reactive, toRefs, onBeforeMount, onMounted } from "vue";
export default {
  name: "",
  setup() {
    console.log("1-开始创建组件-setup");
    const data = reactive({});
    onBeforeMount(() => {
      console.log("2.组件挂载页面之前执行----onBeforeMount");
    });
    onMounted(() => {
      console.log("3.-组件挂载到页面之后执行-------onMounted");
    });
    const close = () => {
      // 关闭视频监控
      console.log("关闭视频监控",this);
      // this.$router.push("/");
    };
    return {
      close,...toRefs(data),
    };
  },
};
</script> -->
<style scoped lang='less'>
.controller {
  height: 60.5rem;
  background: url("../assets/img/videoSurveillance/bg.png") center center no-repeat;
  background-size: 100% 100%;
  position: absolute;
  top: 6rem;
  left: 1rem;
  right: 1rem;

  .videoView {
    width: 100%;
    height: 100%;
    position: absolute;
    background-color: yellow;
  }

  .close {
    background: url("../assets/img/videoSurveillance/close.png") center center no-repeat;
    background-size: 100% 100%;
    width: 1.25rem;
    height: 1.25rem;
    position: absolute;
    top: 0.625rem;
    right: 1.25rem;
    cursor: pointer;
  }

  .leftTopIcon {
    background: url("../assets/img/videoSurveillance/leftTop.png") center center no-repeat;
    background-size: 100% 100%;
    width: 1.5625rem;
    height: 1.5625rem;
    position: absolute;
    top: -0.4375rem;
    left: -0.4375rem;
  }

  .rightTopIcon {
    background: url("../assets/img/videoSurveillance/rightTop.png") center center no-repeat;
    background-size: 100% 100%;
    width: 1.5625rem;
    height: 1.5625rem;
    position: absolute;
    top: -0.4375rem;
    right: -0.4375rem;
  }

  .leftBottomIcon {
    background: url("../assets/img/videoSurveillance/leftBottom.png") center center no-repeat;
    background-size: 100% 100%;
    width: 0.9375rem;
    height: 4.1813rem;
    position: absolute;
    bottom: -0.4375rem;
    left: -0.4375rem;
  }

  .rightBottomIcon {
    background: url("../assets/img/videoSurveillance/rightBottom.png") center center no-repeat;
    background-size: 100% 100%;
    width: 0.9375rem;
    height: 4.1813rem;
    position: absolute;
    bottom: -0.4375rem;
    right: -0.4375rem;
  }

  .top {
    background: url("../assets/img/videoSurveillance/topBg.png") center center no-repeat;
    background-size: 100% 100%;
    width: 100%;
    height: 2.5rem;

    .top-title {
      margin-left: 1.25rem;
      height: 100%;
      line-height: 2.5rem;
      font-size: 1rem;
      color: #fff;
      text-align: left;
    }
  }

  .content {
    width: 100%;
    height: calc(100% - 2.5rem - 1.625rem * 2);
    display: flex;
    justify-content: space-evenly;
    margin-top: 1.625rem;

    .left {
      width: 28rem;
      height: 100%;

      .left-search {
        background: url("../assets/img/videoSurveillance/searchBg.png") center center no-repeat;
        background-size: 100% 100%;
        width: 100%;
        height: 2.5rem;
        border: 1px solid rgba(71, 152, 247, 0.25);
        border-radius: 0.0625rem;

        :deep(.el-input) {
          width: calc(100% - 0.375rem);
          height: calc(100% - 0.375rem);
          margin-top: 0.1875rem;
          font-size: 0.875rem;

          .el-input__wrapper {
            background-color: rgba(71, 152, 247, 0.01);
            box-shadow: none;
            border: 1px solid rgba(71, 152, 247, 0.25);
            border-radius: 0.0625rem;

            .el-input__inner {
              color: #d3deec;
            }
          }
        }
      }

      .left-content {
        margin-top: 0.3125rem;
        height: calc(100% - 2.8125rem);
        overflow: auto;

        :deep(.el-tree) {
          background-color: rgba(71, 152, 247, 0.01);

          .el-tree-node {
            .el-tree-node__content {
              .el-tree-node__label {
                color: #d3deec;
                font-size: 0.875rem;
              }
            }
          }
        }

        //鼠标悬停的备降色
        /deep/ .el-tree-node__content:hover {
          background: #1e374f;
        }

        //选中的背景色
        /deep/.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
          background-color: #1e374f;
        }

        //整体背景色
        //  /deep/.el-tree {
        // background: #00162a;
        // }

        //点击时背景色
        /deep/ .el-tree-node:focus>.el-tree-node__content {
          background-color: #1e374f;
        }
      }
    }

    .line {
      width: 0.0625rem;
      height: 100%;
      opacity: 0.7;
      background: repeating-linear-gradient(to bottom,
          #fff,
          #fff 1px,
          transparent 0.3125rem,
          transparent 0.625rem);
      /* 创建一条虚线背景 */
    }

    .right {
      width: 85rem;
      height: 100%;

      .top-tip {
        background: url("../assets/img/videoSurveillance/tip.png") center center no-repeat;
        background-size: 100% 100%;
        width: 100%;
        height: 2.5rem;
      }

      .right-content {
        display: flex;
        flex-wrap: wrap;
        overflow: auto;
        height: calc(100% - 2.5rem);

        .item {
          border: 1px rgba(79, 160, 255, 0.2) solid;
          width: 41.875rem;
          height: 25.25rem;
          margin-top: 1rem;

          .item-top {
            width: 100%;
            height: 2.25rem;
            // rgba(71, 152, 247, 0.2)
            background: linear-gradient(to right,
                rgba(71, 152, 247, 0.2),
                rgba(71, 152, 247, 0));
            display: flex;
            align-items: center;

            .top-icon {
              background: url("../assets/img/videoSurveillance/supervisoryIcon.png") center center no-repeat;
              background-size: 100% 100%;
              width: 1.25rem;
              height: 1.25rem;
              margin-left: 1rem;
            }

            .top-title {
              margin-left: 1rem;
              font-size: 0.875rem;
              font-weight: 700;
              color: #fff;
            }
          }

          .item-video {
            width: calc(100% - 1.125rem);
            height: calc(100% - 2.25rem - 1.125rem);
            margin-top: 0.5625rem;
            margin-left: 0.5625rem;
            position: relative;
            display: flex;
            justify-content: center;

            .video-bottom {
              position: absolute;
              bottom: 0;
            }

            .video {
              width: 100%;
              height: 100%;
              position: relative;

              .video-bg {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(71, 152, 247, 0);
              }
            }

            .bottom-1 {
              transform: translateY(-40px);
            }
          }
        }
      }
    }
  }
}
</style>