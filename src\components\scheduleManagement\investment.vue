<template>
  <div class="investment">
    <div :class="store.getters.isProjectScreen ? 'investment-title2': 'investment-title'"></div>
    <div class="investment-content">
      <InvestmentProgress
        class="content-item content-item-invest"
        icon="1"
        title="完成总投资比例"
        :progress="(data.completeTotalInvestmentRatio * 100).toFixed(2)"
      ></InvestmentProgress>
      <InvestmentProgress
        class="content-item content-item-debt"
        icon="2"
        title="完成国债投资比例"
        :progress="(data.completeNationalDebtInvestmentRatio * 100).toFixed(2)"
      >
      </InvestmentProgress>
      <div class="content-amount">
        <div class="content-amount-1">
          <investmentAmount
            class="content-item-1"
            title="初设批复投资"
            :amount="data.initialReplyInvestment"
            :type="1"
          ></investmentAmount>
          <investmentAmount
            class="content-item-2"
            title="完成总投资"
            :amount="data.completeTotalInvestment"
            :type="1"
          ></investmentAmount>
        </div>
        <div class="content-amount-1">
          <investmentAmount
            class="content-item-3"
            title="国债资金"
            :amount="data.nationalDebtFund"
            :type="2"
          ></investmentAmount>
          <investmentAmount
            class="content-item-4"
            title="完成国债投资"
            :amount="data.completeNationalDebtInvestment"
            :type="2"
          ></investmentAmount>
        </div>
      </div>
    </div>
  </div>
</template>


<script setup>
import { onMounted, ref, watch } from "vue";
import InvestmentProgress from "../scheduleManagement/InvestmentProgress.vue";
import investmentAmount from "../scheduleManagement/InvestmentAmount.vue";
import api from "../../api/index";
import { useStore } from "vuex";
const store = useStore();
let projectId = store.state.projectId;
let projectId2 = ref('')
const investmentProgressPro = ref("80");
const props = defineProps({
  title: String,
  amount: Number,
  type: Number,
});
let data = ref({
  completeNationalDebtInvestment: 0,
  completeNationalDebtInvestmentRatio: 0,
  completeTotalInvestment: 0,
  completeTotalInvestmentRatio: 0,
  initialReplyInvestment: 0,
  nationalDebtFund: 0,
});

onMounted(() => {
  console.log("组件已挂载", props.icon);

  let id = store.state.projectId;
  console.log("id====", id, "=---=", projectId);
  const sectionId = store.state.sectionId;
  getData({ projectId, sectionId});

});

// watch(
//   () => store.state.projectId,(projectId,oldProjectId) => {
//     console.log('projectId监听->',projectId,oldProjectId)
//     const sectionId = store.state.sectionId
//     getData( {projectId,sectionId} );
//   },
//   () => store.state.projectId,(sectionId,oldSectionId) => {
//     console.log('sectionId监听->',sectionId,oldSectionId)
//     const projectId = store.state.projectId
//     getData( {projectId,sectionId} );
// },
// )
watch(
  () => [store.state.projectId, store.state.sectionId],
  ([projectId, sectionId]) => {
    console.log("监听->", projectId, sectionId);
    projectId2.value = projectId
    getData({ projectId, sectionId });
  }
);

const getData = (params) => {
  api.getInvestmentData(params).then((res) => {
    console.log(res);
    data.value = res.result;
  });
};
</script>

<style lang="less" scoped>
.investment {
  width: 28.75rem;
  height: 25.25rem;

  .investment-title {
    background: url("../../assets/img/investment/title.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .investment-title2 {
    background: url("../../assets/img/investment/title2.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .investment-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 23rem;
    width: 100%;

    .content-item-invest,
    .content-item {
      margin: 1rem 1rem 0 1rem;
    }

    .content-item-debt {
      margin: 1rem 1rem 0 1rem;
    }

    .content-item-3,
    .content-item-4 {
      background: linear-gradient(
        to right,
        rgba(41, 139, 255, 0),
        rgba(41, 139, 255, 0.15),
        rgba(41, 139, 255, 0)
      );
      .content-item-bg {
        background: url("../../assets/img/investment/itemBg2.png") center center
          no-repeat;
        background-size: 100% 100%;
        .content-item-bg-line {
          background: linear-gradient(
            to right,
            gba(41, 139, 255, 0),
            rgba(41, 139, 255, 1),
            rgba(41, 139, 255, 0)
          );
        }
      }
    }

    .content-amount {
      margin: 1.25rem;

      .content-amount-1 {
        display: flex;
        justify-content: space-between;
        margin-top: 1.875rem;
      }
    }
  }
}
</style>
