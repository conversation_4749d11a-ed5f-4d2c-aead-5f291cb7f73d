<template>
  <div class="workplace">
    <div class="workplace-title"></div>
    <div class="workplace-content">
      <item class="item" :type="1" title="建设单位" :content="data.builderOrgName == null ? '' : data.builderOrgName || ''"></item>
      <item class="item" :type="2" title="施工单位" :content="data.constructionOrgName || ''"></item>
      <item class="item" :type="3" title="监理单位" :content="data.supervisionOrgName || ''"></item>
      <item class="item" :type="4" title="造价咨询单位" :content="data.costConsultingOrgName || ''"></item>
      <item class="item" :type="5" title="设计单位" :content="data.designOrgName || ''"></item>
      <item class="item" :type="6" title="全过程检测单位" :content="data.fullProcessDetectionOrgName || ''"></item>
    </div>
  </div>
</template>
  
  <script setup>
import { onMounted, ref, watch } from "vue";
import api from "@/api/index";
import { useStore } from "vuex";
import item from "./item.vue";
const store = useStore();
let data = ref({
  builderOrgName: '',
  constructionOrgName: '',
  costConsultingOrgName: '',
  designOrgName: '',
  fullProcessDetectionOrgName: '',
  supervisionOrgName: '',
});

onMounted(() => {
  if (store.state.sectionId != null) {
    getWorkplaceSectionData({ projectId: '', sectionId:store.state.sectionId });
    }
});

watch(
  () => [store.state.sectionId],
  ([sectionId]) => {
    if (sectionId != null) {
      getWorkplaceSectionData({ projectId:'', sectionId });
    }
  }
);

const getWorkplaceSectionData = async (params) => {
  let res = await api.getWorkplaceSectionData(params);
  data.value = res.result
};
</script>
  
  <style lang="less" scoped>
.workplace {
  width: 28.75rem;
  height: 25.25rem;

  .workplace-title {
    background: url("../../assets/img/workplace/title.png") center center
      no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 100%;
  }

  .workplace-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 28.5rem;
    width: 100%;
    position: absolute;
    overflow: auto;
    padding: 1rem;
    .item {
      width: calc(100% - 2rem);
      height: 3.5rem;
      margin-bottom: 1rem;
    }
  }
}
</style>
  