<template>
  <div class="container">
    <div class="dot">
      <div class="circle"></div>
    </div>
    <div class="animation"></div>
  </div>
</template>
  
  <script setup>
import { onMounted, ref } from "vue";
const props = defineProps({
  title: String,
});

onMounted(() => {
  console.log("组件已挂载", props.icon);
});
</script>
  
<style lang="less" scoped>
.container {
position: relative;
.dot {
    position: absolute;
  width: 0.75rem;
  height: 0.75rem;
  border: #3dffff 0.5px solid;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
  .circle {
    width: 0.5rem;
    height: 0.5rem;
    background-color: #3dffff;
    border-radius: 50%;
  }
}

.animation {
  position: absolute;
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background-color: #3dffff;
  animation: scale 1.5s infinite cubic-bezier(0, 0, 0.49, 1.02);
  filter: blur(0.1rem);
}
@keyframes scale {
  0% {
    transform: scale(1);
  }
  50%,
  75% {
    transform: scale(2);
  }
  78%,
  100% {
    opacity: 0;
  }
}
}
</style>