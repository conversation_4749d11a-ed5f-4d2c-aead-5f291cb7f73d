{"name": "bigscreen-go", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "upload:193": "yarn build && yarn config:ci:193 && yarn upload:ci:193 && node script/upload.js personnel-manager-web http://*************:8282/deploy"}, "dependencies": {"@tarojs/taro": "^3.6.34", "axios": "^1.7.2", "core-js": "^3.8.3", "echarts": "^5.5.0", "element-plus": "^2.7.5", "moment": "^2.30.1", "vue": "^3.2.13", "vue-router": "^4.0.3", "vue3-seamless-scroll": "^2.0.1", "vuex": "^4.0.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "less": "^4.2.0", "less-loader": "^12.2.0", "sass": "^1.77.8", "sass-loader": "^15.0.0"}}