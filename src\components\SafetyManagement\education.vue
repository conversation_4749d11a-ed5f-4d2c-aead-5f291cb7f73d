<template>
  <div class="projectOverview">
    <div class="projectOverview-title"></div>
    <div class="projectOverview-content">
      <div class="education-box">
        <div class="left-x">
          <p>{{ planExecuteRate ? (planExecuteRate).toFixed(2) : 0 }}%</p>
          <img
            class="img1"
            src="../../assets/img/SafetyManagement/计划执行率.png"
            alt=""
          />
          <img
            class="img2"
            src="../../assets/img/SafetyManagement/计划.png"
            alt=""
          />
        </div>
        <img
          class="img1"
          src="../../assets/img/SafetyManagement/隔开线.png"
          alt=""
        />
        <div class="right-x">
          <!-- <img style="margin-left: 3.75rem;" src="../../assets/img/SafetyManagement/计划培训.png" alt="" /> -->
          <div class="san-box">
            <div class="shanx"></div>
            <div id="santj1" style="width: 9.25rem; height: 9.25rem"></div>
            <div class="textimg1-1">
              <p style="font-weight: 700; font-size: 1.5rem">
                {{ Totaltraining }}
              </p>
              <p style="font-weight: 400; font-size: 0.75rem">计划培训</p>
            </div>
          </div>
          <div class="san-box2">
            <div class="shanx"></div>
            <div id="santj2" style="width: 9.25rem; height: 9.25rem"></div>
            <div class="textimg1-2">
              <p style="font-weight: 700; font-size: 1.5rem">
                {{ Actualtotal }}
              </p>
              <p style="font-weight: 400; font-size: 0.75rem">实际培训</p>
            </div>
          </div>
          <!-- <img
            style="margin: 0 3.75rem"
            src="../../assets/img/SafetyManagement/实际培训.png"
            alt=""
          /> -->
          <div class="bottom">
            <div class="son-top" v-for="(item, index) in list" :key="index">
              <div class="yibyan">
                <div
                  class="a-box"
                  :style="{ backgroundColor: item.color }"
                ></div>
                <p>{{ item.name }}</p>
              </div>
              <div class="bianf">
                <p class="p1">{{ item.shuj1 }}</p>
                <p>{{ item.shuj2 }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  
<script setup>
import { onMounted, ref, watch } from "vue";
import overviewItem from "../projectOverview/overviewItem.vue";
import * as echarts from "echarts";
import api from "@/api/index";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
const route = useRoute();
let store = useStore();
onMounted(() => {
  // if (!store.state.projectId && !store.state.sectionId) {
    educationx();
  // }
});
// 监听
watch(
  () => [store.state.projectId, store.state.sectionId],
  (newVal, oldVal) => {
    if (newVal !== oldVal) {
      educationx();
     
    }
    if (store.state.projectId && store.state.sectionId == null) {
      educationx();
      console.log("掉到了");
    }
  }
);
// 扇形统计
let Totaltraining = ref(0); //计划培训总数
let Actualtotal = ref(0); //实际培训总数
let planExecuteRate = ref(0); //计划执行率
const educationx = () => {
  let projectId = store.state.projectId;
  let sectionId = store.state.sectionId;
  let params = {
    projectId: projectId,
    sectionId: sectionId,
  };
  api
    .education(params)
    .then((res) => {
      // 这里处理响应数据

      let data = res.data;
      Actualtotal.value =
        data.actualDailyTraining +
        data.actualOtherTraining +
        data.actualPreClassMeeting +
        data.actualThreeLevelSafetyEducation;
      Totaltraining.value =
        data.planDailyTraining +
        data.planOtherTraining +
        data.planPreClassMeeting +
        data.planThreeLevelSafetyEducation;

        planExecuteRate.value = data.planExecuteRate * 100
      if (route.path == "/safetyManagement") {
        shanxing1(data);
        shanxing2(data);
        listx(data);
      }
    })
    .catch((err) => {
      // 处理错误
      console.log(err, "培训接口请求失败");
    });
};

// 扇形统计图
const shanxing1 = async (data) => {
  let santj1Element = document.getElementById("santj1");
  // 销毁已有的 echarts 实例
  echarts.dispose(santj1Element);

  const santj1 = await echarts.init(document.getElementById("santj1"));
  const canto1 = ref({
    tooltip: {
      trigger: "item",
    },
    legend: {
      top: "5%",
      left: "center",
      show: false,
    },
    series: [
      {
        type: "pie",
        center: ["50%", "50%"],
        radius: ["60%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          borderWidth: 15,
          // normal: {
          //   color: "blue",
          //   opacity: 1,
          //   transform: `scale: (1.2)`,
          // },
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            name: "计划日常培训",
            value: data?.planDailyTraining ? data?.planDailyTraining : 0,
            itemStyle: { color: "#4798F7" },
          },
          {
            name: "计划班前会",
            value: data?.planPreClassMeeting ? data?.planPreClassMeeting : 0,
            itemStyle: { color: "#3DFFFF" },
          },
          {
            name: "计划三级安全教育",
            value: data?.planThreeLevelSafetyEducation
              ? data?.planThreeLevelSafetyEducation
              : 0,
            itemStyle: { color: "#919FFF" },
          },
          {
            name: "其他培训计划",
            value: data?.planOtherTraining ? data?.planOtherTraining : 0,
            itemStyle: { color: "#C4EFEF" },
          },
        ],
      },
    ],
  });
  santj1.setOption(canto1.value);
};
const shanxing2 = async (data) => {
  let santj2Element = document.getElementById("santj2");
  // 销毁已有的 echarts 实例
  echarts.dispose(santj2Element);

  const santj2 = await echarts.init(document.getElementById("santj2"));
  const canto2 = ref({
    tooltip: {
      trigger: "item",
      formatter: null,
    },
    legend: {
      top: "5%",
      left: "center",
      show: false,
    },
    series: [
      {
        // name: "Access From",
        type: "pie",
        center: ["50%", "50%"],
        radius: ["60%", "70%"],
        avoidLabelOverlap: false,
        itemStyle: {
          // normal: {
          //   color: "blue",
          //   opacity: 1,
          //   transform: `scale: (1.2)`,
          // },

          // borderColor: "#fff",
          borderWidth: 2,
        },
        label: {
          show: false,
          position: "center",
        },
        emphasis: {
          label: {
            show: false,
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          {
            name: "实际日常培训",
            value: data?.actualDailyTraining ? data?.actualDailyTraining : 0,
            itemStyle: { color: "#4798F7" },
          },
          {
            name: "	实际班前会",
            value: data?.actualPreClassMeeting
              ? data?.actualPreClassMeeting
              : 0,
            itemStyle: { color: "#3DFFFF" },
          },
          {
            name: "实际三级安全教育",
            value: data?.actualThreeLevelSafetyEducation
              ? data?.actualThreeLevelSafetyEducation
              : 0,
            itemStyle: { color: "#919FFF" },
          },
          {
            name: "	实际其他培训",
            value: data?.actualOtherTraining ? data?.actualOtherTraining : 0,
            itemStyle: { color: "#C4EFEF" },
          },
        ],
      },
    ],
  });

  santj2.setOption(canto2.value);
};
const list = ref([]);
const listx = (data) => {
  list.value = [
    {
      color: "#4798F7",
      name: "日常培训",
      shuj1: data?.planDailyTraining ? data?.planDailyTraining : 0,
      shuj2: data?.actualDailyTraining == 0 || data?.planDailyTraining == 0 ? '0%' : (data?.actualDailyTraining / data?.planDailyTraining * 100).toFixed(2) + '%',
    },
    {
      color: "#3DFFFF",
      name: "班前会",
      shuj1: data?.planPreClassMeeting ? data?.planPreClassMeeting : 0,
      shuj2: data?.actualPreClassMeeting == 0 || data?.planPreClassMeeting == 0 ? '0%' : (data?.actualPreClassMeeting / data?.planPreClassMeeting * 100).toFixed(2) + '%',
    },
    {
      color: "#919FFF",
      name: "三级安全教育",
      shuj1: data?.planThreeLevelSafetyEducation
        ? data?.planThreeLevelSafetyEducation
        : 0,
      shuj2: data?.actualThreeLevelSafetyEducation == 0 || data?.planThreeLevelSafetyEducation == 0 ? '0%' : (data?.actualThreeLevelSafetyEducation / data?.planThreeLevelSafetyEducation * 100).toFixed(2) + '%',
    },
    {
      color: "#C4EFEF",
      name: "其他培训",
      shuj1: data?.planOtherTraining ? data?.planOtherTraining : 0,
      shuj2: data?.actualOtherTraining == 0 || data?.planOtherTraining == 0 ? '0%' : (data?.actualOtherTraining / data?.planOtherTraining * 100).toFixed(2) + '%',
    },
  ];
};
</script>
  
  <style lang="less" scoped>
#santj1 > * {
  /* 具体的图形元素 */
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1.1); /* 缩放因子，这里放大120% */
}
#santj2 > * {
  /* 具体的图形元素 */
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(1.1); /* 缩放因子，这里放大120% */
}
.projectOverview {
  width: 58.5rem;
  height: 13.5625rem;
  .projectOverview-title {
    background: url("../../assets/img/SafetyManagement/安全教育.png") center
      center no-repeat;
    background-size: 100% 100%;
    height: 2.25rem;
    width: 28.75rem;
  }

  .projectOverview-content {
    background-color: rgba(46, 96, 185, 0.1);
    height: 11.0625rem;
    width: 58.5rem;
    // position: absolute;
    // overflow: hidden;
    // overflow-y: auto;
    padding: 0.875rem 2.75rem;
    box-sizing: border-box;
    margin-top: .3125rem;
    > .education-box {
      display: flex;
      height: 100%;
      > .left-x {
        display: flex;
        flex-direction: column;
        align-items: center;
        // border-right: 1px #fff dashed;
        width: 10.75rem;

        > p {
          font-weight: 700;
          font-size: 1.25rem;
          color: #3ce7fe;
          text-shadow: 0 0 16px #27beff;
        }
        > .img1 {
          width: 4.5rem;
          height: 4.25rem;
          margin: 0.75rem 0;
        }
        > .img2 {
          width: 6rem;
          height: 1.5rem;
        }
      }
      > .right-x {
        display: flex;
        align-items: center;
        > .san-box {
          width: 9.25rem;
          margin-left: 3.75rem;
          position: relative;
          background: url("../../assets/img/SafetyManagement/扇形统计3.png")
            center center no-repeat;
          background-size: 100% 100%;
          > .shanx {
            width: 8rem;
            height: 8rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: url("../../assets/img/SafetyManagement/扇形浅蓝2.png")
              center center no-repeat;
            background-size: 100% 100%;
          }
          > .textimg1-1 {
            width: 5.25rem;
            height: 5.25rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: url("../../assets/img/SafetyManagement/扇形浅蓝.png")
              center center no-repeat;
            background-size: 100% 100%;
          }
        }
        .san-box2 {
          width: 9.25rem;
          margin: 0 3.75rem;
          position: relative;
          background: url("../../assets/img/SafetyManagement/扇形统计3.png")
            center center no-repeat;
          background-size: 100% 100%;
          > .shanx {
            width: 8rem;
            height: 8rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: url("../../assets/img/SafetyManagement/扇形浅蓝2.png")
              center center no-repeat;
            background-size: 100% 100%;
          }
          > .textimg1-2 {
            width: 5.25rem;
            height: 5.25rem;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            background: url("../../assets/img/SafetyManagement/扇形浅蓝.png")
              center center no-repeat;
            background-size: 100% 100%;
          }
        }
        > img {
          width: 9.25rem;
        }
        > .bottom {
          width: 10.75rem;
          height: 9.3125rem;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          > .son-top {
            width: 100%;
            height: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            > .yibyan {
              display: flex;
              align-items: center;
              > .a-box {
                width: 0.5rem;
                height: 0.5rem;
                background-color: #f7c034;
                margin-right: 0.25rem;
              }
              > p {
                font-weight: 400;
                font-size: 0.75rem;
                color: #d3deec;
              }
            }
            > .bianf {
              width: 4.75rem;
              display: flex;
              align-items: center;
              justify-content: space-between;
              .p1{
                margin-right: 1rem;
              }
              > p {
                font-size: 0.875rem;
                font-weight: 500;
                
                &:nth-child(2) {
                  // margin-left: 1rem;
                }
              }
            }
          }
          .son-bottom {
            width: 100%;
            height: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            > .yibyan {
              display: flex;
              align-items: center;
              > .a-box {
                width: 0.5rem;
                height: 0.5rem;
                background-color: #ff5046;
                margin-right: 0.25rem;
              }
              > p {
                font-weight: 400;
                font-size: 0.75rem;
                color: #d3deec;
              }
            }
            > .bianf {
              display: flex;
              align-items: center;
              > p {
                font-size: 0.875rem;
                font-weight: 500;
                &:nth-child(2) {
                  margin-left: 1rem;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>